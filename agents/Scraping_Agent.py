from fastapi import FastAPI, Query, HTTPException
import requests
import pandas as pd
from typing import List, Dict, Optional
from pydantic import BaseModel
import logging
from datetime import datetime, timedelta
import asyncio
import aiohttp
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Financial Data Agent", description="Financial Modeling Prep API Agent")

class NewsArticle(BaseModel):
    title: str
    url: str
    source: str = "Financial Modeling Prep"
    published_date: Optional[str] = None
    summary: Optional[str] = None
    sentiment: Optional[str] = None

class CompanyNews(BaseModel):
    symbol: str
    title: str
    url: str
    published_date: str
    text: Optional[str] = None
    site: Optional[str] = None

class EarningsData(BaseModel):
    symbol: str
    date: str
    eps: Optional[float] = None
    eps_estimated: Optional[float] = None
    revenue: Optional[float] = None
    revenue_estimated: Optional[float] = None

class FinancialData_Agent:
    def __init__(self):
        self.api_key = os.getenv("FMP_API_KEY", "demo")  # Use demo key if not provided
        self.base_url = "https://financialmodelingprep.com/api/v3"
        self.session = requests.Session()

        if self.api_key == "demo":
            logger.warning("Using demo API key. Please set FMP_API_KEY for full access.")

    def _make_request(self, endpoint: str, params: Dict = None) -> Dict:
        """Make API request to Financial Modeling Prep"""
        try:
            if params is None:
                params = {}

            params['apikey'] = self.api_key
            url = f"{self.base_url}/{endpoint}"

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()

            return response.json()

        except Exception as e:
            logger.error(f"Error making FMP API request: {e}")
            return []

    def get_company_news(self, symbol: str, limit: int = 10) -> List[Dict]:
        """Get company news from Financial Modeling Prep"""
        try:
            endpoint = f"stock_news"
            params = {
                "tickers": symbol,
                "limit": limit
            }

            data = self._make_request(endpoint, params)

            news_articles = []
            for item in data:
                news_articles.append({
                    'title': item.get('title', ''),
                    'url': item.get('url', ''),
                    'source': 'Financial Modeling Prep',
                    'published_date': item.get('publishedDate', ''),
                    'summary': item.get('text', '')[:500] if item.get('text') else '',
                    'sentiment': self._analyze_sentiment(item.get('title', '') + ' ' + item.get('text', ''))
                })

            return news_articles

        except Exception as e:
            logger.error(f"Error fetching company news: {e}")
            return []

    def get_earnings_calendar(self, symbol: str = None, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get earnings calendar data"""
        try:
            endpoint = "earning_calendar"
            params = {}

            if symbol:
                params['symbol'] = symbol
            if from_date:
                params['from'] = from_date
            if to_date:
                params['to'] = to_date

            data = self._make_request(endpoint, params)

            earnings_data = []
            for item in data:
                earnings_data.append({
                    'symbol': item.get('symbol', ''),
                    'date': item.get('date', ''),
                    'eps': item.get('eps'),
                    'eps_estimated': item.get('epsEstimated'),
                    'revenue': item.get('revenue'),
                    'revenue_estimated': item.get('revenueEstimated'),
                    'time': item.get('time', ''),
                    'updated_from_date': item.get('updatedFromDate', ''),
                    'fiscal_date_ending': item.get('fiscalDateEnding', '')
                })

            return earnings_data

        except Exception as e:
            logger.error(f"Error fetching earnings calendar: {e}")
            return []

    def get_market_news(self, limit: int = 20) -> List[Dict]:
        """Get general market news"""
        try:
            endpoint = "fmp/articles"
            params = {
                "page": 0,
                "size": limit
            }

            data = self._make_request(endpoint, params)

            news_articles = []
            for item in data:
                news_articles.append({
                    'title': item.get('title', ''),
                    'url': item.get('url', ''),
                    'source': 'Financial Modeling Prep',
                    'published_date': item.get('date', ''),
                    'summary': item.get('content', '')[:500] if item.get('content') else '',
                    'sentiment': self._analyze_sentiment(item.get('title', '') + ' ' + item.get('content', ''))
                })

            return news_articles

        except Exception as e:
            logger.error(f"Error fetching market news: {e}")
            return []

    def get_economic_calendar(self, from_date: str = None, to_date: str = None) -> List[Dict]:
        """Get economic calendar events"""
        try:
            endpoint = "economic_calendar"
            params = {}

            if from_date:
                params['from'] = from_date
            if to_date:
                params['to'] = to_date

            data = self._make_request(endpoint, params)

            return data

        except Exception as e:
            logger.error(f"Error fetching economic calendar: {e}")
            return []

    def _analyze_sentiment(self, text: str) -> str:
        """Simple sentiment analysis based on keywords"""
        if not text:
            return "neutral"

        text_lower = text.lower()

        positive_keywords = ['gain', 'rise', 'up', 'bull', 'positive', 'growth', 'strong', 'beat', 'exceed', 'profit', 'revenue', 'earnings']
        negative_keywords = ['fall', 'drop', 'down', 'bear', 'negative', 'decline', 'weak', 'miss', 'below', 'loss', 'deficit']

        positive_count = sum(1 for word in positive_keywords if word in text_lower)
        negative_count = sum(1 for word in negative_keywords if word in text_lower)

        if positive_count > negative_count:
            return "positive"
        elif negative_count > positive_count:
            return "negative"
        else:
            return "neutral"

financial_agent = FinancialData_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Financial Data Agent"}

@app.get("/news", response_model=List[NewsArticle])
async def get_news(
    symbol: str = Query(..., example="AAPL"),
    limit: int = Query(10, ge=1, le=50)
):
    """Get financial news for a symbol"""
    try:
        articles = financial_agent.get_company_news(symbol, limit)

        # Convert to Pydantic models
        news_articles = []
        for article in articles:
            news_articles.append(NewsArticle(**article))

        return news_articles

    except Exception as e:
        logger.error(f"Error getting news: {e}")
        raise HTTPException(status_code=500, detail="Error fetching news")

@app.get("/market-news")
async def get_market_news(limit: int = Query(20, ge=5, le=50)):
    """Get general market news"""
    try:
        articles = financial_agent.get_market_news(limit)

        return {
            "articles": articles,
            "count": len(articles),
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error getting market news: {e}")
        raise HTTPException(status_code=500, detail="Error fetching market news")

@app.get("/earnings-calendar")
async def get_earnings_calendar(
    symbol: str = Query(None, description="Stock symbol (optional)"),
    from_date: str = Query(None, description="From date (YYYY-MM-DD)"),
    to_date: str = Query(None, description="To date (YYYY-MM-DD)")
):
    """Get earnings calendar data"""
    try:
        earnings_data = financial_agent.get_earnings_calendar(symbol, from_date, to_date)

        return {
            "earnings": earnings_data,
            "count": len(earnings_data),
            "symbol": symbol,
            "date_range": f"{from_date} to {to_date}" if from_date and to_date else "All dates"
        }

    except Exception as e:
        logger.error(f"Error getting earnings calendar: {e}")
        raise HTTPException(status_code=500, detail="Error fetching earnings calendar")

@app.get("/economic-calendar")
async def get_economic_calendar(
    from_date: str = Query(None, description="From date (YYYY-MM-DD)"),
    to_date: str = Query(None, description="To date (YYYY-MM-DD)")
):
    """Get economic calendar events"""
    try:
        events = financial_agent.get_economic_calendar(from_date, to_date)

        return {
            "events": events,
            "count": len(events),
            "date_range": f"{from_date} to {to_date}" if from_date and to_date else "All dates"
        }

    except Exception as e:
        logger.error(f"Error getting economic calendar: {e}")
        raise HTTPException(status_code=500, detail="Error fetching economic calendar")

@app.get("/market-sentiment")
async def get_market_sentiment(
    query: str = Query("market outlook", description="Search query for sentiment analysis"),
    limit: int = Query(20, ge=5, le=50)
):
    """Get market sentiment from news articles"""
    try:
        # Get market news for sentiment analysis
        articles = financial_agent.get_market_news(limit)

        # Calculate overall sentiment
        sentiment_counts = {'positive': 0, 'negative': 0, 'neutral': 0}
        for article in articles:
            sentiment = article.get('sentiment', 'neutral')
            sentiment_counts[sentiment] += 1

        total = len(articles)
        overall_sentiment = max(sentiment_counts, key=sentiment_counts.get) if total > 0 else "neutral"

        return {
            "query": query,
            "overall_sentiment": overall_sentiment,
            "sentiment_distribution": {
                "positive": round(sentiment_counts['positive'] / total * 100, 1) if total > 0 else 0,
                "negative": round(sentiment_counts['negative'] / total * 100, 1) if total > 0 else 0,
                "neutral": round(sentiment_counts['neutral'] / total * 100, 1) if total > 0 else 0
            },
            "articles": articles[:10],  # Return top 10 articles with sentiment
            "total_articles": total
        }

    except Exception as e:
        logger.error(f"Error analyzing market sentiment: {e}")
        raise HTTPException(status_code=500, detail="Error analyzing sentiment")

@app.get("/capabilities")
async def get_capabilities():
    """Get agent capabilities"""
    return {
        "data_sources": ["Financial Modeling Prep API"],
        "endpoints": [
            "Company news",
            "Market news",
            "Earnings calendar",
            "Economic calendar",
            "Market sentiment analysis"
        ],
        "api_key_status": "demo" if financial_agent.api_key == "demo" else "configured",
        "features": [
            "Real-time financial news",
            "Earnings calendar data",
            "Economic events",
            "Sentiment analysis"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8002)