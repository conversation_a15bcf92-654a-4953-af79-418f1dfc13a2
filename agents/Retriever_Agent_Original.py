from fastapi import FastAP<PERSON>, Query, HTTPException, UploadFile, File
import faiss
import numpy as np
import pandas as pd
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import logging
from datetime import datetime
import pickle
import os
from sentence_transformers import SentenceTransformer
import json
from pathlib import Path
from dotenv import load_dotenv
import asyncio

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Retriever Agent", description="Vector Store and Retrieval Agent")

class Document(BaseModel):
    id: str
    content: str
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None

class SearchResult(BaseModel):
    document: Document
    score: float
    rank: int

class IndexStats(BaseModel):
    total_documents: int
    index_size: int
    embedding_dimension: int
    last_updated: Optional[str] = None

class Retriever_Agent:
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", index_path: str = "./data/faiss_index"):
        self.model_name = model_name
        self.index_path = Path(index_path)
        self.index_path.mkdir(parents=True, exist_ok=True)

        # Initialize sentence transformer with error handling
        try:
            logger.info(f"Loading sentence transformer model: {model_name}")
            self.encoder = SentenceTransformer(model_name)
            self.embedding_dim = self.encoder.get_sentence_embedding_dimension()
            logger.info(f"Model loaded successfully. Embedding dimension: {self.embedding_dim}")
        except Exception as e:
            logger.error(f"Error loading sentence transformer: {e}")
            logger.info("Using fallback configuration")
            self.encoder = None
            self.embedding_dim = 384  # Default dimension for MiniLM

        # Initialize FAISS index
        self.index = None
        self.documents = {}  # Store document metadata
        self.doc_ids = []    # Store document IDs in order

        # Load existing index if available
        self.load_index()

    def load_index(self):
        """Load existing FAISS index and metadata"""
        try:
            index_file = self.index_path / "index.faiss"
            metadata_file = self.index_path / "metadata.pkl"

            if index_file.exists() and metadata_file.exists():
                # Load FAISS index
                self.index = faiss.read_index(str(index_file))

                # Load metadata
                with open(metadata_file, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data.get('documents', {})
                    self.doc_ids = data.get('doc_ids', [])

                logger.info(f"Loaded index with {len(self.documents)} documents")
            else:
                # Create new index
                self.index = faiss.IndexFlatIP(self.embedding_dim)  # Inner product for cosine similarity
                logger.info("Created new FAISS index")

        except Exception as e:
            logger.error(f"Error loading index: {e}")
            self.index = faiss.IndexFlatIP(self.embedding_dim)

    def save_index(self):
        """Save FAISS index and metadata"""
        try:
            index_file = self.index_path / "index.faiss"
            metadata_file = self.index_path / "metadata.pkl"

            # Save FAISS index
            faiss.write_index(self.index, str(index_file))

            # Save metadata
            metadata = {
                'documents': self.documents,
                'doc_ids': self.doc_ids,
                'last_updated': datetime.now().isoformat()
            }

            with open(metadata_file, 'wb') as f:
                pickle.dump(metadata, f)

            logger.info("Index saved successfully")

        except Exception as e:
            logger.error(f"Error saving index: {e}")

    def add_document(self, doc_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Add a document to the index"""
        try:
            if not self.encoder:
                logger.warning("Encoder not available, cannot add document")
                return False

            # Generate embedding
            embedding = self.encoder.encode([content])[0]

            # Normalize for cosine similarity
            embedding = embedding / np.linalg.norm(embedding)

            # Add to FAISS index
            self.index.add(np.array([embedding], dtype=np.float32))

            # Store metadata
            self.documents[doc_id] = {
                'content': content,
                'metadata': metadata,
                'embedding': embedding.tolist()
            }
            self.doc_ids.append(doc_id)

            return True

        except Exception as e:
            logger.error(f"Error adding document {doc_id}: {e}")
            return False

    def add_documents_batch(self, documents: List[Dict]) -> int:
        """Add multiple documents in batch"""
        added_count = 0

        try:
            contents = [doc['content'] for doc in documents]
            embeddings = self.encoder.encode(contents)

            # Normalize embeddings
            embeddings = embeddings / np.linalg.norm(embeddings, axis=1, keepdims=True)

            # Add to FAISS index
            self.index.add(embeddings.astype(np.float32))

            # Store metadata
            for i, doc in enumerate(documents):
                doc_id = doc['id']
                self.documents[doc_id] = {
                    'content': doc['content'],
                    'metadata': doc.get('metadata', {}),
                    'embedding': embeddings[i].tolist()
                }
                self.doc_ids.append(doc_id)
                added_count += 1

        except Exception as e:
            logger.error(f"Error in batch add: {e}")

        return added_count

    def search(self, query: str, k: int = 10, threshold: float = 0.0) -> List[Dict]:
        """Search for similar documents"""
        try:
            if not self.encoder:
                logger.warning("Encoder not available, cannot search")
                return []

            if self.index.ntotal == 0:
                return []

            # Generate query embedding
            query_embedding = self.encoder.encode([query])[0]
            query_embedding = query_embedding / np.linalg.norm(query_embedding)

            # Search
            scores, indices = self.index.search(
                np.array([query_embedding], dtype=np.float32), k
            )

            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1 or score < threshold:  # FAISS returns -1 for invalid indices
                    continue

                doc_id = self.doc_ids[idx]
                doc_data = self.documents[doc_id]

                results.append({
                    'document': {
                        'id': doc_id,
                        'content': doc_data['content'],
                        'metadata': doc_data['metadata']
                    },
                    'score': float(score),
                    'rank': i + 1
                })

            return results

        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []

    def get_document(self, doc_id: str) -> Optional[Dict]:
        """Get a specific document by ID"""
        return self.documents.get(doc_id)

    def delete_document(self, doc_id: str) -> bool:
        """Delete a document (note: FAISS doesn't support deletion, so we mark as deleted)"""
        try:
            if doc_id in self.documents:
                self.documents[doc_id]['metadata']['deleted'] = True
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting document {doc_id}: {e}")
            return False

    def get_stats(self) -> Dict:
        """Get index statistics"""
        active_docs = sum(1 for doc in self.documents.values()
                         if not doc.get('metadata', {}).get('deleted', False))

        return {
            'total_documents': active_docs,
            'index_size': self.index.ntotal if self.index else 0,
            'embedding_dimension': self.embedding_dim,
            'last_updated': datetime.now().isoformat()
        }

# Initialize retriever agent
retriever_agent = Retriever_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        encoder_status = "available" if retriever_agent.encoder else "unavailable"
        index_status = "initialized" if retriever_agent.index else "uninitialized"

        return {
            "status": "healthy",
            "service": "Retriever Agent",
            "encoder_status": encoder_status,
            "index_status": index_status,
            "embedding_dimension": retriever_agent.embedding_dim,
            "total_documents": len(retriever_agent.documents)
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "service": "Retriever Agent",
            "error": str(e)
        }

@app.post("/documents")
async def add_document(
    doc_id: str = Query(..., description="Unique document ID"),
    content: str = Query(..., description="Document content"),
    metadata: str = Query("{}", description="JSON metadata")
):
    """Add a single document to the index"""
    try:
        metadata_dict = json.loads(metadata)
        success = retriever_agent.add_document(doc_id, content, metadata_dict)

        if success:
            retriever_agent.save_index()
            return {"message": "Document added successfully", "doc_id": doc_id}
        else:
            raise HTTPException(status_code=500, detail="Failed to add document")

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON in metadata")
    except Exception as e:
        logger.error(f"Error adding document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/documents/batch")
async def add_documents_batch(documents: List[Document]):
    """Add multiple documents in batch"""
    try:
        docs_data = []
        for doc in documents:
            docs_data.append({
                'id': doc.id,
                'content': doc.content,
                'metadata': doc.metadata
            })

        added_count = retriever_agent.add_documents_batch(docs_data)
        retriever_agent.save_index()

        return {
            "message": f"Added {added_count} documents successfully",
            "added_count": added_count,
            "total_requested": len(documents)
        }

    except Exception as e:
        logger.error(f"Error in batch add: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/search", response_model=List[SearchResult])
async def search_documents(
    query: str = Query(..., description="Search query"),
    k: int = Query(10, ge=1, le=100, description="Number of results to return"),
    threshold: float = Query(0.0, ge=0.0, le=1.0, description="Minimum similarity threshold")
):
    """Search for similar documents"""
    try:
        results = retriever_agent.search(query, k, threshold)

        search_results = []
        for result in results:
            search_results.append(SearchResult(
                document=Document(**result['document']),
                score=result['score'],
                rank=result['rank']
            ))

        return search_results

    except Exception as e:
        logger.error(f"Error in search: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/documents/{doc_id}")
async def get_document(doc_id: str):
    """Get a specific document by ID"""
    try:
        doc = retriever_agent.get_document(doc_id)

        if not doc:
            raise HTTPException(status_code=404, detail="Document not found")

        if doc.get('metadata', {}).get('deleted', False):
            raise HTTPException(status_code=404, detail="Document has been deleted")

        return {
            "id": doc_id,
            "content": doc['content'],
            "metadata": doc['metadata']
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.delete("/documents/{doc_id}")
async def delete_document(doc_id: str):
    """Delete a document"""
    try:
        success = retriever_agent.delete_document(doc_id)

        if success:
            retriever_agent.save_index()
            return {"message": "Document deleted successfully", "doc_id": doc_id}
        else:
            raise HTTPException(status_code=404, detail="Document not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/stats", response_model=IndexStats)
async def get_stats():
    """Get index statistics"""
    try:
        stats = retriever_agent.get_stats()
        return IndexStats(**stats)

    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/index/rebuild")
async def rebuild_index():
    """Rebuild the index (useful for optimization)"""
    try:
        # Get all non-deleted documents
        active_docs = []
        for doc_id, doc_data in retriever_agent.documents.items():
            if not doc_data.get('metadata', {}).get('deleted', False):
                active_docs.append({
                    'id': doc_id,
                    'content': doc_data['content'],
                    'metadata': doc_data['metadata']
                })

        # Create new index
        retriever_agent.index = faiss.IndexFlatIP(retriever_agent.embedding_dim)
        retriever_agent.documents = {}
        retriever_agent.doc_ids = []

        # Re-add all documents
        added_count = retriever_agent.add_documents_batch(active_docs)
        retriever_agent.save_index()

        return {
            "message": "Index rebuilt successfully",
            "documents_count": added_count
        }

    except Exception as e:
        logger.error(f"Error rebuilding index: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)