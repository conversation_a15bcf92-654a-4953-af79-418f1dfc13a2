from fastapi import FastAP<PERSON>, Query, HTTPException
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import logging
from datetime import datetime
import json
import pickle
from pathlib import Path
from dotenv import load_dotenv
import re
from collections import Counter
import math

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Simple Retriever Agent", description="Simple Document Storage and Retrieval Agent")

class Document(BaseModel):
    id: str
    content: str
    metadata: Dict[str, Any]

class SearchResult(BaseModel):
    document: Document
    score: float
    rank: int

class IndexStats(BaseModel):
    total_documents: int
    index_size: int
    last_updated: Optional[str] = None

class SimpleRetriever_Agent:
    def __init__(self, index_path: str = "./data/simple_index"):
        self.index_path = Path(index_path)
        self.index_path.mkdir(parents=True, exist_ok=True)
        
        # Simple in-memory storage
        self.documents = {}  # Store document data
        self.word_index = {}  # Inverted index for search
        
        # Load existing data if available
        self.load_index()
        logger.info("Simple Retriever Agent initialized")
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization"""
        # Convert to lowercase and extract words
        text = text.lower()
        words = re.findall(r'\b[a-zA-Z]{2,}\b', text)
        return words
    
    def _calculate_tf_idf(self, query_words: List[str], doc_words: List[str]) -> float:
        """Calculate TF-IDF similarity score"""
        if not query_words or not doc_words:
            return 0.0
        
        # Term frequency in document
        doc_word_count = Counter(doc_words)
        doc_length = len(doc_words)
        
        # Calculate score
        score = 0.0
        for word in query_words:
            if word in doc_word_count:
                tf = doc_word_count[word] / doc_length
                # Simple IDF approximation
                idf = math.log(len(self.documents) + 1)
                score += tf * idf
        
        return score
    
    def _build_word_index(self):
        """Build inverted index for faster search"""
        self.word_index = {}
        
        for doc_id, doc_data in self.documents.items():
            words = self._tokenize(doc_data['content'])
            for word in set(words):  # Unique words only
                if word not in self.word_index:
                    self.word_index[word] = []
                self.word_index[word].append(doc_id)
    
    def load_index(self):
        """Load existing index and metadata"""
        try:
            metadata_file = self.index_path / "simple_metadata.pkl"
            
            if metadata_file.exists():
                with open(metadata_file, 'rb') as f:
                    data = pickle.load(f)
                    self.documents = data.get('documents', {})
                
                # Rebuild word index
                self._build_word_index()
                logger.info(f"Loaded index with {len(self.documents)} documents")
            else:
                logger.info("No existing index found, starting fresh")
                
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            self.documents = {}
            self.word_index = {}
    
    def save_index(self):
        """Save index and metadata"""
        try:
            metadata_file = self.index_path / "simple_metadata.pkl"
            
            metadata = {
                'documents': self.documents,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(metadata_file, 'wb') as f:
                pickle.dump(metadata, f)
                
            logger.info("Index saved successfully")
            
        except Exception as e:
            logger.error(f"Error saving index: {e}")
    
    def add_document(self, doc_id: str, content: str, metadata: Dict[str, Any]) -> bool:
        """Add a document to the index"""
        try:
            # Store document
            self.documents[doc_id] = {
                'content': content,
                'metadata': metadata,
                'added_at': datetime.now().isoformat()
            }
            
            # Update word index
            words = self._tokenize(content)
            for word in set(words):
                if word not in self.word_index:
                    self.word_index[word] = []
                if doc_id not in self.word_index[word]:
                    self.word_index[word].append(doc_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding document {doc_id}: {e}")
            return False
    
    def search(self, query: str, k: int = 10, threshold: float = 0.0) -> List[Dict]:
        """Search for similar documents using keyword matching"""
        try:
            if not self.documents:
                return []
            
            query_words = self._tokenize(query)
            if not query_words:
                return []
            
            # Find candidate documents
            candidate_docs = set()
            for word in query_words:
                if word in self.word_index:
                    candidate_docs.update(self.word_index[word])
            
            if not candidate_docs:
                return []
            
            # Calculate scores
            scored_docs = []
            for doc_id in candidate_docs:
                if doc_id in self.documents:
                    doc_data = self.documents[doc_id]
                    doc_words = self._tokenize(doc_data['content'])
                    score = self._calculate_tf_idf(query_words, doc_words)
                    
                    if score >= threshold:
                        scored_docs.append((doc_id, score))
            
            # Sort by score and return top k
            scored_docs.sort(key=lambda x: x[1], reverse=True)
            
            results = []
            for i, (doc_id, score) in enumerate(scored_docs[:k]):
                doc_data = self.documents[doc_id]
                results.append({
                    'document': {
                        'id': doc_id,
                        'content': doc_data['content'],
                        'metadata': doc_data['metadata']
                    },
                    'score': float(score),
                    'rank': i + 1
                })
            
            return results
            
        except Exception as e:
            logger.error(f"Error in search: {e}")
            return []
    
    def get_document(self, doc_id: str) -> Optional[Dict]:
        """Get a specific document by ID"""
        return self.documents.get(doc_id)
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete a document"""
        try:
            if doc_id in self.documents:
                del self.documents[doc_id]
                # Rebuild word index
                self._build_word_index()
                return True
            return False
        except Exception as e:
            logger.error(f"Error deleting document {doc_id}: {e}")
            return False
    
    def get_stats(self) -> Dict:
        """Get index statistics"""
        return {
            'total_documents': len(self.documents),
            'index_size': len(self.word_index),
            'last_updated': datetime.now().isoformat()
        }

# Initialize retriever agent
retriever_agent = SimpleRetriever_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        return {
            "status": "healthy", 
            "service": "Simple Retriever Agent",
            "total_documents": len(retriever_agent.documents),
            "index_size": len(retriever_agent.word_index),
            "type": "keyword_based"
        }
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "service": "Simple Retriever Agent", 
            "error": str(e)
        }

@app.post("/documents")
async def add_document(
    doc_id: str = Query(..., description="Unique document ID"),
    content: str = Query(..., description="Document content"),
    metadata: str = Query("{}", description="JSON metadata")
):
    """Add a single document to the index"""
    try:
        metadata_dict = json.loads(metadata)
        success = retriever_agent.add_document(doc_id, content, metadata_dict)
        
        if success:
            retriever_agent.save_index()
            return {"message": "Document added successfully", "doc_id": doc_id}
        else:
            raise HTTPException(status_code=500, detail="Failed to add document")
            
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON in metadata")
    except Exception as e:
        logger.error(f"Error adding document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/search", response_model=List[SearchResult])
async def search_documents(
    query: str = Query(..., description="Search query"),
    k: int = Query(10, ge=1, le=100, description="Number of results to return"),
    threshold: float = Query(0.0, ge=0.0, description="Minimum similarity threshold")
):
    """Search for similar documents"""
    try:
        results = retriever_agent.search(query, k, threshold)
        
        search_results = []
        for result in results:
            search_results.append(SearchResult(
                document=Document(**result['document']),
                score=result['score'],
                rank=result['rank']
            ))
        
        return search_results
        
    except Exception as e:
        logger.error(f"Error in search: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/documents/{doc_id}")
async def get_document(doc_id: str):
    """Get a specific document by ID"""
    try:
        doc = retriever_agent.get_document(doc_id)
        
        if not doc:
            raise HTTPException(status_code=404, detail="Document not found")
        
        return {
            "id": doc_id,
            "content": doc['content'],
            "metadata": doc['metadata']
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting document: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/stats", response_model=IndexStats)
async def get_stats():
    """Get index statistics"""
    try:
        stats = retriever_agent.get_stats()
        return IndexStats(**stats)
        
    except Exception as e:
        logger.error(f"Error getting stats: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8003)
