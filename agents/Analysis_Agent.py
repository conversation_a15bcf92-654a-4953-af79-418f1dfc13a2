from fastapi import FastAPI, Query, HTTPException
import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import logging
from datetime import datetime, timedelta
import yfinance as yf
import requests
from scipy import stats
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Analysis Agent", description="Financial Analysis and Risk Assessment Agent")

class PortfolioPosition(BaseModel):
    symbol: str
    shares: float
    avg_cost: float
    current_price: Optional[float] = None
    market_value: Optional[float] = None
    unrealized_pnl: Optional[float] = None
    weight: Optional[float] = None

class RiskMetrics(BaseModel):
    symbol: str
    volatility: float
    beta: Optional[float] = None
    var_95: float  # Value at Risk 95%
    max_drawdown: float
    sharpe_ratio: Optional[float] = None
    correlation_to_market: Optional[float] = None

class PortfolioAnalysis(BaseModel):
    total_value: float
    total_pnl: float
    total_pnl_percent: float
    positions: List[PortfolioPosition]
    risk_metrics: Dict[str, Any]
    sector_allocation: Dict[str, float]
    geographic_allocation: Dict[str, float]

class EarningsAnalysis(BaseModel):
    symbol: str
    expected_eps: Optional[float] = None
    actual_eps: Optional[float] = None
    surprise_percent: Optional[float] = None
    revenue_expected: Optional[float] = None
    revenue_actual: Optional[float] = None
    revenue_surprise_percent: Optional[float] = None
    guidance: Optional[str] = None

class Analysis_Agent:
    def __init__(self):
        self.market_benchmark = "^GSPC"  # S&P 500

    def calculate_volatility(self, prices: pd.Series, period: int = 252) -> float:
        """Calculate annualized volatility"""
        returns = prices.pct_change().dropna()
        return returns.std() * np.sqrt(period)

    def calculate_beta(self, stock_prices: pd.Series, market_prices: pd.Series) -> float:
        """Calculate beta relative to market"""
        stock_returns = stock_prices.pct_change().dropna()
        market_returns = market_prices.pct_change().dropna()

        # Align the series
        aligned_data = pd.concat([stock_returns, market_returns], axis=1).dropna()
        if len(aligned_data) < 30:  # Need sufficient data
            return None

        stock_aligned = aligned_data.iloc[:, 0]
        market_aligned = aligned_data.iloc[:, 1]

        covariance = np.cov(stock_aligned, market_aligned)[0][1]
        market_variance = np.var(market_aligned)

        return covariance / market_variance if market_variance != 0 else None

    def calculate_var(self, returns: pd.Series, confidence: float = 0.95) -> float:
        """Calculate Value at Risk"""
        return np.percentile(returns, (1 - confidence) * 100)

    def calculate_max_drawdown(self, prices: pd.Series) -> float:
        """Calculate maximum drawdown"""
        cumulative = (1 + prices.pct_change()).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()

    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        excess_returns = returns.mean() * 252 - risk_free_rate  # Annualized
        volatility = returns.std() * np.sqrt(252)  # Annualized
        return excess_returns / volatility if volatility != 0 else None

    def get_stock_data(self, symbol: str, period: str = "1y") -> pd.DataFrame:
        """Get stock data using yfinance"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            return data
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return pd.DataFrame()

    def get_stock_info(self, symbol: str) -> Dict:
        """Get stock information"""
        try:
            ticker = yf.Ticker(symbol)
            return ticker.info
        except Exception as e:
            logger.error(f"Error fetching info for {symbol}: {e}")
            return {}

    def analyze_portfolio_risk(self, positions: List[Dict]) -> Dict:
        """Analyze portfolio risk metrics"""
        try:
            portfolio_data = []
            total_value = 0

            # Get market data for beta calculation
            market_data = self.get_stock_data(self.market_benchmark)
            market_prices = market_data['Close'] if not market_data.empty else pd.Series()

            for position in positions:
                symbol = position['symbol']
                shares = position['shares']
                avg_cost = position['avg_cost']

                # Get stock data
                stock_data = self.get_stock_data(symbol)
                if stock_data.empty:
                    continue

                current_price = stock_data['Close'].iloc[-1]
                market_value = shares * current_price
                unrealized_pnl = market_value - (shares * avg_cost)

                # Calculate risk metrics
                returns = stock_data['Close'].pct_change().dropna()
                volatility = self.calculate_volatility(stock_data['Close'])
                beta = self.calculate_beta(stock_data['Close'], market_prices)
                var_95 = self.calculate_var(returns, 0.95)
                max_drawdown = self.calculate_max_drawdown(stock_data['Close'])
                sharpe_ratio = self.calculate_sharpe_ratio(returns)

                portfolio_data.append({
                    'symbol': symbol,
                    'shares': shares,
                    'avg_cost': avg_cost,
                    'current_price': current_price,
                    'market_value': market_value,
                    'unrealized_pnl': unrealized_pnl,
                    'volatility': volatility,
                    'beta': beta,
                    'var_95': var_95,
                    'max_drawdown': max_drawdown,
                    'sharpe_ratio': sharpe_ratio
                })

                total_value += market_value

            # Calculate portfolio weights
            for item in portfolio_data:
                item['weight'] = item['market_value'] / total_value if total_value > 0 else 0

            # Calculate portfolio-level metrics
            portfolio_volatility = 0
            portfolio_beta = 0

            for item in portfolio_data:
                portfolio_volatility += (item['weight'] ** 2) * (item['volatility'] ** 2)
                if item['beta']:
                    portfolio_beta += item['weight'] * item['beta']

            portfolio_volatility = np.sqrt(portfolio_volatility)

            return {
                'positions': portfolio_data,
                'total_value': total_value,
                'portfolio_volatility': portfolio_volatility,
                'portfolio_beta': portfolio_beta,
                'diversification_ratio': len(portfolio_data)
            }

        except Exception as e:
            logger.error(f"Error analyzing portfolio risk: {e}")
            return {}

    def analyze_sector_allocation(self, symbols: List[str]) -> Dict[str, float]:
        """Analyze sector allocation"""
        sector_allocation = {}

        for symbol in symbols:
            try:
                info = self.get_stock_info(symbol)
                sector = info.get('sector', 'Unknown')

                if sector in sector_allocation:
                    sector_allocation[sector] += 1
                else:
                    sector_allocation[sector] = 1

            except Exception as e:
                logger.warning(f"Could not get sector for {symbol}: {e}")
                continue

        # Convert to percentages
        total = sum(sector_allocation.values())
        if total > 0:
            sector_allocation = {k: (v / total) * 100 for k, v in sector_allocation.items()}

        return sector_allocation

    def analyze_earnings_surprise(self, symbol: str) -> Dict:
        """Analyze earnings surprise for a symbol"""
        try:
            ticker = yf.Ticker(symbol)

            # Get earnings data
            earnings = ticker.earnings_dates
            if earnings is None or earnings.empty:
                return {"error": "No earnings data available"}

            # Get the most recent earnings
            recent_earnings = earnings.head(1)

            if not recent_earnings.empty:
                row = recent_earnings.iloc[0]

                expected_eps = row.get('EPS Estimate')
                actual_eps = row.get('Reported EPS')

                surprise_percent = None
                if expected_eps and actual_eps and expected_eps != 0:
                    surprise_percent = ((actual_eps - expected_eps) / abs(expected_eps)) * 100

                return {
                    'symbol': symbol,
                    'expected_eps': expected_eps,
                    'actual_eps': actual_eps,
                    'surprise_percent': surprise_percent,
                    'earnings_date': recent_earnings.index[0].strftime('%Y-%m-%d')
                }

            return {"error": "No recent earnings data"}

        except Exception as e:
            logger.error(f"Error analyzing earnings for {symbol}: {e}")
            return {"error": str(e)}

analysis_agent = Analysis_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Analysis Agent"}

@app.get("/risk-metrics/{symbol}", response_model=RiskMetrics)
async def get_risk_metrics(
    symbol: str,
    period: str = Query("1y", enum=["1mo", "3mo", "6mo", "1y", "2y"])
):
    """Get risk metrics for a single stock"""
    try:
        # Get stock data
        stock_data = analysis_agent.get_stock_data(symbol, period)
        if stock_data.empty:
            raise HTTPException(status_code=404, detail=f"No data found for {symbol}")

        # Get market data for beta calculation
        market_data = analysis_agent.get_stock_data(analysis_agent.market_benchmark, period)

        # Calculate metrics
        returns = stock_data['Close'].pct_change().dropna()
        volatility = analysis_agent.calculate_volatility(stock_data['Close'])
        beta = analysis_agent.calculate_beta(stock_data['Close'], market_data['Close']) if not market_data.empty else None
        var_95 = analysis_agent.calculate_var(returns, 0.95)
        max_drawdown = analysis_agent.calculate_max_drawdown(stock_data['Close'])
        sharpe_ratio = analysis_agent.calculate_sharpe_ratio(returns)

        # Calculate correlation to market
        correlation = None
        if not market_data.empty:
            stock_returns = stock_data['Close'].pct_change().dropna()
            market_returns = market_data['Close'].pct_change().dropna()
            aligned_data = pd.concat([stock_returns, market_returns], axis=1).dropna()
            if len(aligned_data) > 30:
                correlation = aligned_data.iloc[:, 0].corr(aligned_data.iloc[:, 1])

        return RiskMetrics(
            symbol=symbol,
            volatility=round(volatility, 4),
            beta=round(beta, 3) if beta else None,
            var_95=round(var_95, 4),
            max_drawdown=round(max_drawdown, 4),
            sharpe_ratio=round(sharpe_ratio, 3) if sharpe_ratio else None,
            correlation_to_market=round(correlation, 3) if correlation else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating risk metrics: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/portfolio-analysis")
async def analyze_portfolio(positions: List[PortfolioPosition]):
    """Analyze a portfolio of positions"""
    try:
        # Convert to dict format for analysis
        positions_data = []
        for pos in positions:
            positions_data.append({
                'symbol': pos.symbol,
                'shares': pos.shares,
                'avg_cost': pos.avg_cost
            })

        # Perform analysis
        analysis_result = analysis_agent.analyze_portfolio_risk(positions_data)

        if not analysis_result:
            raise HTTPException(status_code=500, detail="Failed to analyze portfolio")

        # Calculate totals
        total_value = analysis_result['total_value']
        total_cost = sum(pos.shares * pos.avg_cost for pos in positions)
        total_pnl = total_value - total_cost
        total_pnl_percent = (total_pnl / total_cost * 100) if total_cost > 0 else 0

        # Get sector allocation
        symbols = [pos.symbol for pos in positions]
        sector_allocation = analysis_agent.analyze_sector_allocation(symbols)

        # Format positions
        formatted_positions = []
        for pos_data in analysis_result['positions']:
            formatted_positions.append(PortfolioPosition(
                symbol=pos_data['symbol'],
                shares=pos_data['shares'],
                avg_cost=pos_data['avg_cost'],
                current_price=round(pos_data['current_price'], 2),
                market_value=round(pos_data['market_value'], 2),
                unrealized_pnl=round(pos_data['unrealized_pnl'], 2),
                weight=round(pos_data['weight'] * 100, 2)
            ))

        return PortfolioAnalysis(
            total_value=round(total_value, 2),
            total_pnl=round(total_pnl, 2),
            total_pnl_percent=round(total_pnl_percent, 2),
            positions=formatted_positions,
            risk_metrics={
                'portfolio_volatility': round(analysis_result['portfolio_volatility'], 4),
                'portfolio_beta': round(analysis_result['portfolio_beta'], 3),
                'diversification_ratio': analysis_result['diversification_ratio']
            },
            sector_allocation=sector_allocation,
            geographic_allocation={"US": 100.0}  # Simplified for demo
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing portfolio: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/earnings-analysis/{symbol}", response_model=EarningsAnalysis)
async def get_earnings_analysis(symbol: str):
    """Get earnings analysis for a symbol"""
    try:
        result = analysis_agent.analyze_earnings_surprise(symbol)

        if "error" in result:
            raise HTTPException(status_code=404, detail=result["error"])

        return EarningsAnalysis(
            symbol=result['symbol'],
            expected_eps=result.get('expected_eps'),
            actual_eps=result.get('actual_eps'),
            surprise_percent=round(result['surprise_percent'], 2) if result.get('surprise_percent') else None
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing earnings: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/sector-analysis")
async def get_sector_analysis(
    symbols: List[str] = Query(..., description="List of stock symbols")
):
    """Get sector allocation analysis"""
    try:
        sector_allocation = analysis_agent.analyze_sector_allocation(symbols)

        return {
            "symbols": symbols,
            "sector_allocation": sector_allocation,
            "analysis_date": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Error analyzing sectors: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/correlation-matrix")
async def get_correlation_matrix(
    symbols: List[str] = Query(..., description="List of stock symbols"),
    period: str = Query("6mo", enum=["1mo", "3mo", "6mo", "1y"])
):
    """Get correlation matrix for a list of symbols"""
    try:
        price_data = {}

        # Collect price data for all symbols
        for symbol in symbols:
            data = analysis_agent.get_stock_data(symbol, period)
            if not data.empty:
                price_data[symbol] = data['Close'].pct_change().dropna()

        if len(price_data) < 2:
            raise HTTPException(status_code=400, detail="Need at least 2 valid symbols")

        # Create correlation matrix
        df = pd.DataFrame(price_data)
        correlation_matrix = df.corr()

        # Convert to dict format
        result = {}
        for i, symbol1 in enumerate(correlation_matrix.index):
            result[symbol1] = {}
            for j, symbol2 in enumerate(correlation_matrix.columns):
                result[symbol1][symbol2] = round(correlation_matrix.iloc[i, j], 3)

        return {
            "correlation_matrix": result,
            "symbols": symbols,
            "period": period,
            "analysis_date": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error calculating correlation matrix: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8004)