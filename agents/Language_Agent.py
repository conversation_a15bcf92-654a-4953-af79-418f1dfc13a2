from fastapi import FastAP<PERSON>, Query, HTTPException
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import logging
from datetime import datetime
import json
from dotenv import load_dotenv
import os
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM, pipeline
import re

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Language Agent", description="Open-Source LLM Language Processing Agent")

class AnalysisRequest(BaseModel):
    query: str
    context_data: Dict[str, Any]
    analysis_type: str = "market_brief"

class AnalysisResponse(BaseModel):
    query: str
    analysis: str
    confidence: float
    sources: List[str]
    timestamp: str

class Language_Agent:
    def __init__(self):
        self.model_name = os.getenv("LOCAL_LLM_MODEL", "TinyLlama/TinyLlama-1.1B-Chat-v1.0")
        self.device = "mps" if torch.backends.mps.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")

        # Initialize local LLM
        try:
            logger.info(f"Loading model: {self.model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_name,
                torch_dtype=torch.float16 if self.device != "cpu" else torch.float32,
                device_map="auto" if self.device != "cpu" else None,
                trust_remote_code=True
            )

            # Create text generation pipeline
            self.generator = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device != "cpu" else -1,
                torch_dtype=torch.float16 if self.device != "cpu" else torch.float32,
                max_new_tokens=512,
                temperature=0.3,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )

            logger.info("Local LLM loaded successfully")

        except Exception as e:
            logger.error(f"Error loading local LLM: {e}")
            self.generator = None

    def format_prompt(self, template: str, **kwargs) -> str:
        """Format prompt template with provided data"""
        try:
            return template.format(**kwargs)
        except Exception as e:
            logger.error(f"Error formatting prompt: {e}")
            return template

    def generate_response(self, prompt: str, max_length: int = 512) -> str:
        """Generate response using local LLM"""
        try:
            if not self.generator:
                return self._fallback_response(prompt)

            # Format prompt for TinyLlama chat format
            chat_prompt = f"<|system|>\nYou are a professional financial analyst.\n<|user|>\n{prompt}\n<|assistant|>\n"

            # Generate response
            outputs = self.generator(
                chat_prompt,
                max_new_tokens=max_length,
                temperature=0.3,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id,
                eos_token_id=self.tokenizer.eos_token_id,
                return_full_text=False
            )

            response = outputs[0]['generated_text'].strip()

            # Clean up response
            response = self._clean_response(response)

            return response

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return self._fallback_response(prompt)

    def _clean_response(self, response: str) -> str:
        """Clean and format the generated response"""
        # Remove any remaining special tokens
        response = re.sub(r'<\|.*?\|>', '', response)

        # Remove excessive whitespace
        response = re.sub(r'\n\s*\n', '\n\n', response)
        response = response.strip()

        # Ensure response is not too short
        if len(response) < 50:
            return "Based on the provided data, I can see market activity and portfolio positions. For detailed analysis, please ensure all data sources are properly connected."

        return response

    def _fallback_response(self, prompt: str) -> str:
        """Provide fallback response when LLM is not available"""
        return f"Analysis request received: {prompt[:100]}... Local LLM is not available. Please check the model configuration."

    def generate_market_brief(self, query: str, context_data: Dict) -> Dict:
        """Generate a comprehensive market brief"""
        try:
            # Create market brief prompt
            prompt = f"""
            You are a professional financial analyst. Provide a market brief for the following query:

            Query: {query}

            Market Data Summary:
            - Portfolio positions: {len(context_data.get('portfolio', {}).get('positions', []))} holdings
            - Market data available: {bool(context_data.get('market', {}))}
            - News items: {len(context_data.get('news', []))}

            Portfolio Data: {json.dumps(context_data.get('portfolio', {}), indent=2)[:500]}...
            Market Data: {json.dumps(context_data.get('market', {}), indent=2)[:500]}...

            Please provide a comprehensive market brief that addresses:
            1. Current portfolio status and risk exposure
            2. Market conditions and trends
            3. Key insights from available data
            4. Risk assessment and recommendations

            Keep the response professional, concise, and actionable.
            """

            response = self.generate_response(prompt, max_length=400)

            return {
                'analysis': response,
                'confidence': 0.75,
                'sources': ['Local LLM Analysis', 'Market Data', 'Portfolio Data']
            }

        except Exception as e:
            logger.error(f"Error generating market brief: {e}")
            return self._fallback_analysis(query, context_data, "market_brief")

    def analyze_earnings(self, query: str, context_data: Dict) -> Dict:
        """Analyze earnings data and surprises"""
        try:
            prompt = f"""
            You are a financial analyst specializing in earnings analysis.

            Query: {query}

            Earnings Data: {json.dumps(context_data.get('earnings', {}), indent=2)[:300]}...
            Stock Data: {json.dumps(context_data.get('stock_data', {}), indent=2)[:300]}...

            Analyze the earnings data and provide insights on:
            1. Earnings surprises (positive or negative)
            2. Impact on stock performance
            3. Future outlook and implications
            4. Investment recommendations

            Be specific about numbers and percentages where available.
            """

            response = self.generate_response(prompt, max_length=350)

            return {
                'analysis': response,
                'confidence': 0.70,
                'sources': ['Local LLM Analysis', 'Earnings Data']
            }

        except Exception as e:
            logger.error(f"Error analyzing earnings: {e}")
            return self._fallback_analysis(query, context_data, "earnings")

    def assess_risk(self, query: str, context_data: Dict) -> Dict:
        """Provide risk assessment"""
        try:
            prompt = f"""
            You are a risk management specialist providing portfolio risk assessment.

            Risk Metrics: {json.dumps(context_data.get('risk_metrics', {}), indent=2)[:300]}...
            Portfolio Data: {json.dumps(context_data.get('portfolio', {}), indent=2)[:300]}...

            Provide a detailed risk assessment covering:
            1. Current risk exposure levels
            2. Concentration risks by sector/geography
            3. Volatility and correlation analysis
            4. Risk mitigation recommendations

            Use specific metrics and provide actionable insights.
            """

            response = self.generate_response(prompt, max_length=400)

            return {
                'analysis': response,
                'confidence': 0.72,
                'sources': ['Local LLM Analysis', 'Risk Metrics']
            }

        except Exception as e:
            logger.error(f"Error assessing risk: {e}")
            return self._fallback_analysis(query, context_data, "risk")

    def _fallback_analysis(self, query: str, context_data: Dict, analysis_type: str) -> Dict:
        """Provide fallback analysis when LLM is not available"""
        fallback_responses = {
            "market_brief": f"""
            Market Brief Analysis:

            Query: {query}

            Based on available data:
            - Portfolio positions: {len(context_data.get('portfolio', {}).get('positions', []))} holdings
            - Market data available for analysis
            - Financial data processed

            The system has processed your request. For enhanced AI-powered insights,
            please ensure the local LLM model is properly loaded.
            """,
            "earnings": f"""
            Earnings Analysis:

            Query: {query}

            Earnings data summary available for analysis.
            Basic financial metrics have been processed.
            """,
            "risk": f"""
            Risk Assessment:

            Query: {query}

            Risk metrics data available for analysis.
            Portfolio risk calculations have been completed.
            """
        }

        return {
            'analysis': fallback_responses.get(analysis_type, "Analysis completed with available data."),
            'confidence': 0.5,
            'sources': ['Basic Analysis']
        }

language_agent = Language_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "Language Agent"}

@app.post("/analyze", response_model=AnalysisResponse)
async def analyze_data(request: AnalysisRequest):
    """Analyze data and generate insights"""
    try:
        if request.analysis_type == "market_brief":
            result = language_agent.generate_market_brief(request.query, request.context_data)
        elif request.analysis_type == "earnings":
            result = language_agent.analyze_earnings(request.query, request.context_data)
        elif request.analysis_type == "risk":
            result = language_agent.assess_risk(request.query, request.context_data)
        else:
            raise HTTPException(status_code=400, detail="Invalid analysis type")

        return AnalysisResponse(
            query=request.query,
            analysis=result['analysis'],
            confidence=result['confidence'],
            sources=result['sources'],
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in analysis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/market-brief")
async def generate_market_brief(
    query: str = Query(..., description="Market brief query"),
    portfolio_data: str = Query("{}", description="JSON portfolio data"),
    market_data: str = Query("{}", description="JSON market data"),
    news_data: str = Query("[]", description="JSON news data")
):
    """Generate market brief"""
    try:
        context_data = {
            'portfolio': json.loads(portfolio_data),
            'market': json.loads(market_data),
            'news': json.loads(news_data)
        }

        result = language_agent.generate_market_brief(query, context_data)

        return {
            "query": query,
            "analysis": result['analysis'],
            "confidence": result['confidence'],
            "sources": result['sources'],
            "timestamp": datetime.now().isoformat()
        }

    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid JSON in input data")
    except Exception as e:
        logger.error(f"Error generating market brief: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/capabilities")
async def get_capabilities():
    """Get agent capabilities"""
    return {
        "analysis_types": ["market_brief", "earnings", "risk"],
        "llm_available": language_agent.generator is not None,
        "model_name": language_agent.model_name,
        "device": language_agent.device,
        "supported_queries": [
            "Portfolio risk exposure analysis",
            "Earnings surprise analysis",
            "Market sentiment assessment",
            "Sector allocation review",
            "Risk mitigation recommendations"
        ]
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8005)
