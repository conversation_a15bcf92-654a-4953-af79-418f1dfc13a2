from fastapi import FastAPI, Query, HTTPException
import requests
from datetime import datetime, timedelta
import os
import yfinance as yf
import pandas as pd
from typing import Dict, List, Optional
from pydantic import BaseModel
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="API Agent", description="Financial Data API Agent")
ALPHA_VANTAGE_API = os.getenv("ALPHA_VANTAGE_API")

class StockData(BaseModel):
    symbol: str
    date: str
    open: float
    high: float
    low: float
    close: float
    volume: int
    change_percent: Optional[float] = None

class MarketSummary(BaseModel):
    symbol: str
    current_price: float
    change: float
    change_percent: float
    volume: int
    market_cap: Optional[float] = None
    pe_ratio: Optional[float] = None

class API_Agent:
    def __init__(self):
        self.alpha_vantage_api = ALPHA_VANTAGE_API

    @staticmethod
    def get_daily_prices_alpha_vantage(symbol: str) -> Dict:
        """Get daily prices from Alpha Vantage API"""
        if not ALPHA_VANTAGE_API:
            logger.warning("Alpha Vantage API key not found, falling back to Yahoo Finance")
            return {}

        url = "https://www.alphavantage.co/query"
        params = {
            "function": "TIME_SERIES_DAILY_ADJUSTED",  # Fixed typo: was "funciton"
            "symbol": symbol,
            "apikey": ALPHA_VANTAGE_API,
        }

        try:
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            data = response.json()

            if "Error Message" in data:
                logger.error(f"Alpha Vantage API error: {data['Error Message']}")
                return {}

            return data.get("Time Series (Daily)", {})
        except Exception as e:
            logger.error(f"Error fetching data from Alpha Vantage: {e}")
            return {}

    @staticmethod
    def get_stock_data_yahoo(symbol: str, period: str = "1mo") -> pd.DataFrame:
        """Get stock data from Yahoo Finance as fallback"""
        try:
            ticker = yf.Ticker(symbol)
            data = ticker.history(period=period)
            return data
        except Exception as e:
            logger.error(f"Error fetching data from Yahoo Finance: {e}")
            return pd.DataFrame()

    @staticmethod
    def get_stock_info_yahoo(symbol: str) -> Dict:
        """Get detailed stock information from Yahoo Finance"""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            return info
        except Exception as e:
            logger.error(f"Error fetching stock info: {e}")
            return {}

api_agent = API_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "API Agent"}

@app.get("/stock-summary", response_model=MarketSummary)
async def stock_summary(symbol: str = Query(..., example="TSM")):
    """Get current stock summary"""
    try:
        # Try Alpha Vantage first, fallback to Yahoo Finance
        prices = api_agent.get_daily_prices_alpha_vantage(symbol)

        if not prices:
            # Fallback to Yahoo Finance
            yahoo_data = api_agent.get_stock_data_yahoo(symbol, "5d")
            if yahoo_data.empty:
                raise HTTPException(status_code=404, detail="No data found for symbol")

            latest = yahoo_data.iloc[-1]
            previous = yahoo_data.iloc[-2] if len(yahoo_data) > 1 else latest

            change = latest['Close'] - previous['Close']
            change_percent = (change / previous['Close']) * 100

            # Get additional info
            info = api_agent.get_stock_info_yahoo(symbol)

            return MarketSummary(
                symbol=symbol,
                current_price=round(latest['Close'], 2),
                change=round(change, 2),
                change_percent=round(change_percent, 2),
                volume=int(latest['Volume']),
                market_cap=info.get('marketCap'),
                pe_ratio=info.get('trailingPE')
            )
        else:
            # Process Alpha Vantage data
            dates = sorted(prices.keys(), reverse=True)
            if len(dates) < 2:
                raise HTTPException(status_code=404, detail="Insufficient data")

            latest_date = dates[0]
            previous_date = dates[1]

            latest_data = prices[latest_date]
            previous_data = prices[previous_date]

            current_price = float(latest_data['4. close'])
            previous_price = float(previous_data['4. close'])

            change = current_price - previous_price
            change_percent = (change / previous_price) * 100

            return MarketSummary(
                symbol=symbol,
                current_price=round(current_price, 2),
                change=round(change, 2),
                change_percent=round(change_percent, 2),
                volume=int(float(latest_data['6. volume']))
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in stock_summary: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/stock-history", response_model=List[StockData])
async def stock_history(
    symbol: str = Query(..., example="TSM"),
    days: int = Query(30, ge=1, le=365)
):
    """Get historical stock data"""
    try:
        # Use Yahoo Finance for historical data
        yahoo_data = api_agent.get_stock_data_yahoo(symbol, f"{days}d")

        if yahoo_data.empty:
            raise HTTPException(status_code=404, detail="No historical data found")

        result = []
        for date, row in yahoo_data.iterrows():
            # Calculate change percent from previous day
            prev_close = None
            if len(result) > 0:
                prev_close = result[-1].close

            change_percent = None
            if prev_close:
                change_percent = ((row['Close'] - prev_close) / prev_close) * 100

            result.append(StockData(
                symbol=symbol,
                date=date.strftime('%Y-%m-%d'),
                open=round(row['Open'], 2),
                high=round(row['High'], 2),
                low=round(row['Low'], 2),
                close=round(row['Close'], 2),
                volume=int(row['Volume']),
                change_percent=round(change_percent, 2) if change_percent else None
            ))

        return result[-days:]  # Return last N days

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in stock_history: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.get("/market-movers")
async def market_movers():
    """Get market movers for major indices and popular stocks"""
    symbols = ["^GSPC", "^IXIC", "^DJI", "AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"]
    movers = []

    for symbol in symbols:
        try:
            data = api_agent.get_stock_data_yahoo(symbol, "2d")
            if not data.empty and len(data) >= 2:
                latest = data.iloc[-1]
                previous = data.iloc[-2]

                change = latest['Close'] - previous['Close']
                change_percent = (change / previous['Close']) * 100

                movers.append({
                    "symbol": symbol,
                    "price": round(latest['Close'], 2),
                    "change": round(change, 2),
                    "change_percent": round(change_percent, 2)
                })
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            continue

    return {"movers": movers}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)