from fastapi import FastAPI, HTTPException, Query
from typing import Optional
from pydantic import BaseModel
import logging
from datetime import datetime
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="Text Processing Agent", description="Simplified Text Processing Agent")

class TextProcessingRequest(BaseModel):
    text: str
    operation: str = "analyze"  # analyze, summarize, format

class TextProcessingResponse(BaseModel):
    original_text: str
    processed_text: str
    operation: str
    timestamp: str

class Text_Agent:
    def __init__(self):
        logger.info("Text Processing Agent initialized")

    def analyze_text(self, text: str) -> str:
        """Analyze text for financial keywords and sentiment"""
        financial_keywords = [
            'earnings', 'revenue', 'profit', 'loss', 'stock', 'market', 'investment',
            'portfolio', 'risk', 'volatility', 'dividend', 'growth', 'decline'
        ]

        text_lower = text.lower()
        found_keywords = [kw for kw in financial_keywords if kw in text_lower]

        # Simple sentiment analysis
        positive_words = ['gain', 'profit', 'growth', 'increase', 'rise', 'bull', 'positive']
        negative_words = ['loss', 'decline', 'fall', 'decrease', 'bear', 'negative', 'drop']

        positive_count = sum(1 for word in positive_words if word in text_lower)
        negative_count = sum(1 for word in negative_words if word in text_lower)

        if positive_count > negative_count:
            sentiment = "positive"
        elif negative_count > positive_count:
            sentiment = "negative"
        else:
            sentiment = "neutral"

        analysis = f"""
        Text Analysis Results:
        - Length: {len(text)} characters
        - Financial keywords found: {', '.join(found_keywords) if found_keywords else 'None'}
        - Sentiment: {sentiment}
        - Positive indicators: {positive_count}
        - Negative indicators: {negative_count}
        """

        return analysis.strip()

    def summarize_text(self, text: str, max_sentences: int = 3) -> str:
        """Simple text summarization"""
        sentences = text.split('.')
        sentences = [s.strip() for s in sentences if s.strip()]

        if len(sentences) <= max_sentences:
            return text

        # Take first, middle, and last sentences for simple summary
        if max_sentences == 3 and len(sentences) >= 3:
            summary_sentences = [
                sentences[0],
                sentences[len(sentences)//2],
                sentences[-1]
            ]
        else:
            summary_sentences = sentences[:max_sentences]

        return '. '.join(summary_sentences) + '.'

    def format_text(self, text: str) -> str:
        """Format text for better readability"""
        # Basic formatting
        formatted = text.strip()

        # Ensure proper sentence spacing
        formatted = formatted.replace('.', '. ')
        formatted = formatted.replace('  ', ' ')

        # Capitalize first letter of sentences
        sentences = formatted.split('. ')
        sentences = [s.capitalize() for s in sentences]
        formatted = '. '.join(sentences)

        return formatted

text_agent = Text_Agent()

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "Text Processing Agent"
    }

@app.post("/process-text", response_model=TextProcessingResponse)
async def process_text(request: TextProcessingRequest):
    """Process text based on operation type"""
    try:
        if not request.text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")

        if request.operation == "analyze":
            processed_text = text_agent.analyze_text(request.text)
        elif request.operation == "summarize":
            processed_text = text_agent.summarize_text(request.text)
        elif request.operation == "format":
            processed_text = text_agent.format_text(request.text)
        else:
            raise HTTPException(status_code=400, detail="Invalid operation. Use: analyze, summarize, or format")

        return TextProcessingResponse(
            original_text=request.text,
            processed_text=processed_text,
            operation=request.operation,
            timestamp=datetime.now().isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing text: {e}")
        raise HTTPException(status_code=500, detail="Text processing failed")

@app.get("/analyze-text")
async def analyze_text_get(text: str = Query(..., description="Text to analyze")):
    """Analyze text for financial content and sentiment"""
    try:
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")

        analysis = text_agent.analyze_text(text)

        return {
            "original_text": text,
            "analysis": analysis,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing text: {e}")
        raise HTTPException(status_code=500, detail="Text analysis failed")

@app.get("/summarize-text")
async def summarize_text_get(
    text: str = Query(..., description="Text to summarize"),
    max_sentences: int = Query(3, ge=1, le=10, description="Maximum sentences in summary")
):
    """Summarize text"""
    try:
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")

        summary = text_agent.summarize_text(text, max_sentences)

        return {
            "original_text": text,
            "summary": summary,
            "max_sentences": max_sentences,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error summarizing text: {e}")
        raise HTTPException(status_code=500, detail="Text summarization failed")

@app.get("/format-text")
async def format_text_get(text: str = Query(..., description="Text to format")):
    """Format text for better readability"""
    try:
        if not text.strip():
            raise HTTPException(status_code=400, detail="Text cannot be empty")

        formatted = text_agent.format_text(text)

        return {
            "original_text": text,
            "formatted_text": formatted,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error formatting text: {e}")
        raise HTTPException(status_code=500, detail="Text formatting failed")

@app.get("/capabilities")
async def get_capabilities():
    """Get text processing agent capabilities"""
    return {
        "operations": ["analyze", "summarize", "format"],
        "features": [
            "Financial keyword detection",
            "Sentiment analysis",
            "Text summarization",
            "Text formatting",
            "Readability improvement"
        ],
        "supported_languages": ["English (primary)"],
        "max_text_length": "No specific limit"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8006)
