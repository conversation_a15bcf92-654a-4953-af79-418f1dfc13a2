import streamlit as st
import requests
import json
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time
import io
import base64
from typing import List, Dict

# Configure Streamlit page
st.set_page_config(
    page_title="Finance Assistant",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Constants
ORCHESTRATOR_URL = "http://localhost:8000"

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-message {
        color: #28a745;
        font-weight: bold;
    }
    .error-message {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

def call_orchestrator(endpoint: str, method: str = "GET", **kwargs) -> Dict:
    """Call the orchestrator API"""
    try:
        url = f"{ORCHESTRATOR_URL}{endpoint}"

        if method == "GET":
            response = requests.get(url, params=kwargs.get('params', {}), timeout=30)
        elif method == "POST":
            if 'files' in kwargs:
                response = requests.post(url, files=kwargs['files'], timeout=30)
            else:
                response = requests.post(
                    url,
                    json=kwargs.get('json', {}),
                    params=kwargs.get('params', {}),
                    timeout=30
                )

        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        st.error(f"Error calling API: {e}")
        return {"error": str(e)}
    except Exception as e:
        st.error(f"Unexpected error: {e}")
        return {"error": str(e)}

def display_market_summary(market_data: Dict):
    """Display market summary in a nice format"""
    if "stock_summaries" in market_data:
        st.subheader("📊 Market Summary")

        cols = st.columns(len(market_data["stock_summaries"]))

        for i, (symbol, data) in enumerate(market_data["stock_summaries"].items()):
            with cols[i]:
                if "error" not in data:
                    price = data.get("current_price", 0)
                    change = data.get("change", 0)
                    change_pct = data.get("change_percent", 0)

                    # Color based on change
                    color = "green" if change >= 0 else "red"
                    arrow = "↗️" if change >= 0 else "↘️"

                    st.markdown(f"""
                    <div class="metric-card">
                        <h4>{symbol}</h4>
                        <h2 style="color: {color};">${price:.2f}</h2>
                        <p style="color: {color};">{arrow} {change:+.2f} ({change_pct:+.2f}%)</p>
                    </div>
                    """, unsafe_allow_html=True)
                else:
                    st.error(f"Error loading {symbol}: {data.get('error', 'Unknown error')}")

def display_portfolio_analysis(analysis_data: Dict):
    """Display portfolio analysis results"""
    if "portfolio_analysis" in analysis_data:
        portfolio = analysis_data["portfolio_analysis"]

        st.subheader("💼 Portfolio Analysis")

        # Portfolio overview
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Value", f"${portfolio.get('total_value', 0):,.2f}")

        with col2:
            pnl = portfolio.get('total_pnl', 0)
            st.metric("Total P&L", f"${pnl:,.2f}", delta=f"{portfolio.get('total_pnl_percent', 0):.2f}%")

        with col3:
            risk_metrics = portfolio.get('risk_metrics', {})
            volatility = risk_metrics.get('portfolio_volatility', 0)
            st.metric("Portfolio Volatility", f"{volatility:.2%}")

        with col4:
            beta = risk_metrics.get('portfolio_beta', 0)
            st.metric("Portfolio Beta", f"{beta:.2f}")

        # Positions table
        if "positions" in portfolio:
            st.subheader("📋 Positions")
            positions_df = pd.DataFrame(portfolio["positions"])

            if not positions_df.empty:
                # Format the dataframe for display
                display_df = positions_df[['symbol', 'shares', 'current_price', 'market_value', 'unrealized_pnl', 'weight']].copy()
                display_df.columns = ['Symbol', 'Shares', 'Current Price', 'Market Value', 'Unrealized P&L', 'Weight (%)']

                # Format currency columns
                for col in ['Current Price', 'Market Value', 'Unrealized P&L']:
                    display_df[col] = display_df[col].apply(lambda x: f"${x:,.2f}")

                st.dataframe(display_df, use_container_width=True)

        # Sector allocation chart
        if "sector_allocation" in portfolio:
            st.subheader("🏭 Sector Allocation")
            sector_data = portfolio["sector_allocation"]

            if sector_data:
                fig = px.pie(
                    values=list(sector_data.values()),
                    names=list(sector_data.keys()),
                    title="Portfolio Sector Allocation"
                )
                st.plotly_chart(fig, use_container_width=True)

def main():
    """Main Streamlit application"""

    # Header
    st.markdown('<h1 class="main-header">🤖 AI Finance Assistant</h1>', unsafe_allow_html=True)

    # Sidebar
    st.sidebar.title("🎛️ Control Panel")

    # Check system health
    with st.sidebar:
        if st.button("🔍 Check System Health"):
            with st.spinner("Checking system health..."):
                health_data = call_orchestrator("/health")

                if "error" not in health_data:
                    st.success("✅ System is healthy!")

                    # Show agent status
                    st.subheader("Agent Status")
                    for agent, status in health_data.get("agents", {}).items():
                        if status.get("status") == "healthy":
                            st.success(f"✅ {agent.title()} Agent")
                        else:
                            st.error(f"❌ {agent.title()} Agent")
                else:
                    st.error("❌ System health check failed")

    # Main content tabs
    tab1, tab2, tab3, tab4 = st.tabs(["📈 Market Brief", "💼 Portfolio Analysis", "📝 Text Processing", "⚙️ System Status"])

    with tab1:
        st.header("📈 Market Brief Generator")

        col1, col2 = st.columns([2, 1])

        with col1:
            query = st.text_input(
                "Enter your market query:",
                value="What's the current market outlook for tech stocks?",
                help="Ask about market conditions, stock performance, or risk analysis"
            )

        with col2:
            symbols = st.multiselect(
                "Select symbols to analyze:",
                options=["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA", "AMZN", "META", "NFLX"],
                default=["AAPL", "GOOGL", "TSLA"],
                help="Choose stocks to include in the analysis"
            )

        include_voice = st.checkbox("🔊 Include voice response", help="Generate audio response")

        if st.button("🚀 Generate Market Brief", type="primary"):
            if query and symbols:
                with st.spinner("Generating market brief..."):
                    result = call_orchestrator(
                        "/market-brief",
                        method="POST",
                        json={
                            "query": query,
                            "symbols": symbols,
                            "include_voice": include_voice
                        }
                    )

                    if "error" not in result:
                        # Display market data
                        if "market_data" in result:
                            display_market_summary(result["market_data"])

                        # Display analysis
                        if "analysis" in result:
                            st.subheader("🧠 AI Analysis")
                            analysis_text = result["analysis"].get("analysis", "No analysis available")
                            st.write(analysis_text)

                            # Show confidence and sources
                            col1, col2 = st.columns(2)
                            with col1:
                                confidence = result["analysis"].get("confidence", 0)
                                st.metric("Confidence", f"{confidence:.1%}")
                            with col2:
                                sources = result["analysis"].get("sources", [])
                                st.write("**Sources:**", ", ".join(sources))

                        # Voice response
                        if include_voice and "voice_response" in result:
                            st.subheader("🔊 Voice Response")
                            st.info("Voice response generated! (Audio playback would be implemented here)")

                    else:
                        st.error(f"Error generating market brief: {result.get('error', 'Unknown error')}")
            else:
                st.warning("Please enter a query and select at least one symbol.")

    with tab2:
        st.header("💼 Portfolio Analysis")

        # Initialize session state
        if 'positions' not in st.session_state:
            st.session_state.positions = [{"symbol": "AAPL", "shares": 100.0, "avg_cost": 150.0}]

        # Add position button (outside form)
        if st.button("➕ Add New Position"):
            st.session_state.positions.append({"symbol": "", "shares": 0.0, "avg_cost": 0.0})

        # Portfolio form
        with st.form("portfolio_form"):
            positions = []

            for i, pos in enumerate(st.session_state.positions):
                st.write(f"**Position {i+1}**")
                col1, col2, col3 = st.columns([2, 1, 1])

                with col1:
                    symbol = st.text_input(f"Symbol", value=pos["symbol"], key=f"symbol_{i}")
                with col2:
                    shares = st.number_input(f"Shares", value=pos["shares"], min_value=0.0, key=f"shares_{i}")
                with col3:
                    avg_cost = st.number_input(f"Avg Cost ($)", value=pos["avg_cost"], min_value=0.0, key=f"cost_{i}")

                if symbol and shares > 0 and avg_cost > 0:
                    positions.append({"symbol": symbol.upper(), "shares": shares, "avg_cost": avg_cost})

                st.divider()

            # Form submit button
            analyze_portfolio = st.form_submit_button("📊 Analyze Portfolio", type="primary")

        # Remove position button (outside form)
        if len(st.session_state.positions) > 1:
            remove_index = st.selectbox("Remove position:",
                                      options=range(len(st.session_state.positions)),
                                      format_func=lambda x: f"Position {x+1}: {st.session_state.positions[x]['symbol'] or 'Empty'}")
            if st.button("🗑️ Remove Selected Position"):
                st.session_state.positions.pop(remove_index)
                st.rerun()

        if analyze_portfolio and positions:
            with st.spinner("Analyzing portfolio..."):
                result = call_orchestrator(
                    "/portfolio-analysis",
                    method="POST",
                    json={
                        "positions": positions,
                        "query": "Analyze my portfolio risk exposure and provide recommendations"
                    }
                )

                if "error" not in result:
                    display_portfolio_analysis(result)

                    # Display insights
                    if "insights" in result:
                        st.subheader("🧠 AI Insights")
                        insights_text = result["insights"].get("analysis", "No insights available")
                        st.write(insights_text)
                else:
                    st.error(f"Error analyzing portfolio: {result.get('error', 'Unknown error')}")

    with tab3:
        st.header("📝 Text Processing Assistant")

        st.info("💬 Process text queries with AI-powered analysis, summarization, and formatting.")

        # Text input
        query_text = st.text_area(
            "Enter your text or financial query:",
            value="Apple Inc. reported strong quarterly earnings with revenue growth of 15% year-over-year.",
            height=100,
            help="Enter any text for processing or ask financial questions"
        )

        # Operation selection
        operation = st.selectbox(
            "Select processing operation:",
            options=["analyze", "summarize", "format"],
            help="Choose how you want to process the text"
        )

        if st.button("🔍 Process Text", type="primary"):
            if query_text.strip():
                with st.spinner("Processing text..."):
                    result = call_orchestrator(
                        "/text-query",
                        method="POST",
                        params={"query": query_text, "operation": operation}
                    )

                    if "error" not in result:
                        st.subheader("📝 Original Query")
                        st.write(result.get('original_query', 'No query available'))

                        st.subheader("🔍 Text Analysis")
                        text_analysis = result.get('text_analysis', {})
                        if text_analysis:
                            st.write(f"**Operation:** {text_analysis.get('operation', 'N/A')}")
                            st.write("**Processed Text:**")
                            st.write(text_analysis.get('processed_text', 'No analysis available'))

                        st.subheader("🤖 AI Response")
                        st.write(result.get('response_text', 'No response available'))
                    else:
                        st.error(f"Error processing text: {result.get('error', 'Unknown error')}")
            else:
                st.warning("Please enter some text to process.")

        # Quick text analysis demo
        st.subheader("🚀 Quick Analysis")
        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📊 Analyze Sample"):
                sample_text = "Tesla stock surged 12% after beating earnings expectations with strong EV delivery numbers."
                with st.spinner("Analyzing..."):
                    result = call_orchestrator(
                        "/text-query",
                        method="POST",
                        params={"query": sample_text, "operation": "analyze"}
                    )
                    if "error" not in result:
                        st.success("Analysis completed!")
                        st.write(result.get('text_analysis', {}).get('processed_text', 'No analysis'))

        with col2:
            if st.button("📝 Summarize Sample"):
                sample_text = "The Federal Reserve announced a 0.25% interest rate increase. This decision comes amid concerns about inflation. Market analysts expect this to impact technology stocks negatively. However, financial sector stocks may benefit from higher rates."
                with st.spinner("Summarizing..."):
                    result = call_orchestrator(
                        "/text-query",
                        method="POST",
                        params={"query": sample_text, "operation": "summarize"}
                    )
                    if "error" not in result:
                        st.success("Summary completed!")
                        st.write(result.get('text_analysis', {}).get('processed_text', 'No summary'))

        with col3:
            if st.button("✨ Format Sample"):
                sample_text = "apple inc reported strong earnings.revenue was up 15%.the stock price increased significantly."
                with st.spinner("Formatting..."):
                    result = call_orchestrator(
                        "/text-query",
                        method="POST",
                        params={"query": sample_text, "operation": "format"}
                    )
                    if "error" not in result:
                        st.success("Formatting completed!")
                        st.write(result.get('text_analysis', {}).get('processed_text', 'No formatting'))

    with tab4:
        st.header("⚙️ System Status")

        if st.button("🔄 Refresh Status"):
            with st.spinner("Checking system status..."):
                status_data = call_orchestrator("/agents/status")

                if "error" not in status_data:
                    for agent_name, agent_data in status_data.items():
                        with st.expander(f"🤖 {agent_name.title()} Agent"):
                            health = agent_data.get("health", {})
                            capabilities = agent_data.get("capabilities", {})

                            col1, col2 = st.columns(2)

                            with col1:
                                st.subheader("Health Status")
                                if health.get("status") == "healthy":
                                    st.success("✅ Healthy")
                                else:
                                    st.error(f"❌ Unhealthy: {health.get('error', 'Unknown error')}")

                                st.write(f"**URL:** {agent_data.get('url', 'Unknown')}")

                            with col2:
                                st.subheader("Capabilities")
                                if capabilities:
                                    st.json(capabilities)
                                else:
                                    st.write("No capabilities data available")
                else:
                    st.error("Failed to retrieve system status")

if __name__ == "__main__":
    main()
