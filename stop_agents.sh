#!/bin/bash

# Multi-Agent Finance Assistant Stop Script
# This script stops all running agents

echo "🛑 Stopping Multi-Agent Finance Assistant..."

# Function to stop a service
stop_service() {
    local service_name=$1
    local pid_file="logs/${service_name,,}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            echo "🔴 Stopping $service_name (PID: $pid)..."
            kill $pid
            sleep 2
            
            # Force kill if still running
            if ps -p $pid > /dev/null 2>&1; then
                echo "   ⚠️  Force killing $service_name..."
                kill -9 $pid
            fi
            
            echo "   ✅ $service_name stopped"
        else
            echo "   ℹ️  $service_name was not running"
        fi
        rm -f "$pid_file"
    else
        echo "   ℹ️  No PID file found for $service_name"
    fi
}

# Stop all services
stop_service "Streamlit"
stop_service "Orchestrator"
stop_service "Voice_Agent"
stop_service "Language_Agent"
stop_service "Analysis_Agent"
stop_service "Retriever_Agent"
stop_service "Scraping_Agent"
stop_service "API_Agent"

# Kill any remaining Python processes related to our agents
echo "🧹 Cleaning up any remaining processes..."
pkill -f "agents/"
pkill -f "orchestrator/main.py"
pkill -f "streamlit_app/main.py"

echo ""
echo "✅ All services stopped successfully!"
echo "📝 Logs are preserved in the logs/ directory"
