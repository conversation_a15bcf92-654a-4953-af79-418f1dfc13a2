#!/bin/bash

# Multi-Agent Finance Assistant Start<PERSON> <PERSON>t
# This script starts all agents in the correct order

echo "🚀 Starting Multi-Agent Finance Assistant..."

# Check if virtual environment is activated
if [[ "$VIRTUAL_ENV" == "" ]]; then
    echo "⚠️  Warning: Virtual environment not detected. Activating RAGA environment..."
    source RAGA/bin/activate
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p data/faiss_index
mkdir -p logs
mkdir -p data/raw
mkdir -p data/processed

# Function to start an agent in the background
start_agent() {
    local agent_name=$1
    local agent_file=$2
    local port=$3

    echo "🤖 Starting $agent_name on port $port..."
    python $agent_file > logs/${agent_name,,}_agent.log 2>&1 &
    local pid=$!
    echo $pid > logs/${agent_name,,}_agent.pid
    echo "   ✅ $agent_name started with PID $pid"
    sleep 2
}

# Start agents in order
echo "🔄 Starting agents..."

start_agent "API" "agents/API_Agent.py" "8001"
start_agent "Financial_Data" "agents/Scraping_Agent.py" "8002"
start_agent "Retriever" "agents/Retriever_Agent.py" "8003"
start_agent "Analysis" "agents/Analysis_Agent.py" "8004"
start_agent "Language" "agents/Language_Agent.py" "8005"
start_agent "Text_Processing" "agents/Voice_Agent.py" "8006"

# Wait a bit for agents to start
echo "⏳ Waiting for agents to initialize..."
sleep 5

# Start orchestrator
echo "🎯 Starting Orchestrator..."
python orchestrator/main.py > logs/orchestrator.log 2>&1 &
orchestrator_pid=$!
echo $orchestrator_pid > logs/orchestrator.pid
echo "   ✅ Orchestrator started with PID $orchestrator_pid"

# Wait for orchestrator to start
sleep 3

# Start Streamlit app
echo "🌐 Starting Streamlit Web Interface..."
streamlit run streamlit_app/main.py --server.port 8501 --server.address 0.0.0.0 > logs/streamlit.log 2>&1 &
streamlit_pid=$!
echo $streamlit_pid > logs/streamlit.pid
echo "   ✅ Streamlit started with PID $streamlit_pid"

echo ""
echo "🎉 All services started successfully!"
echo ""
echo "📊 Access Points:"
echo "   • Streamlit UI: http://localhost:8501"
echo "   • Orchestrator API: http://localhost:8000"
echo "   • API Documentation: http://localhost:8000/docs"
echo ""
echo "🔍 Health Check:"
echo "   curl http://localhost:8000/health"
echo ""
echo "📝 Logs are available in the logs/ directory"
echo "🛑 To stop all services, run: ./stop_agents.sh"
echo ""
echo "✨ Happy analyzing! 📈"
