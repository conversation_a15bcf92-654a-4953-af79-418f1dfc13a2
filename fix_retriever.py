#!/usr/bin/env python3
"""
Fix and restart the Retriever Agent
"""

import subprocess
import time
import requests
import sys
import os

def kill_retriever_agent():
    """Kill any existing Retriever Agent process"""
    try:
        # Kill process on port 8003
        result = subprocess.run(['lsof', '-ti', ':8003'], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    subprocess.run(['kill', pid], check=True)
                    print(f"🔴 Killed process {pid} on port 8003")
                except subprocess.CalledProcessError:
                    subprocess.run(['kill', '-9', pid], check=True)
                    print(f"🔴 Force killed process {pid}")
    except Exception as e:
        print(f"⚠️  Error killing processes: {e}")

def check_dependencies():
    """Check if sentence-transformers is properly installed"""
    try:
        import sentence_transformers
        print("✅ sentence-transformers available")
        return True
    except ImportError:
        print("❌ sentence-transformers not found")
        print("Installing sentence-transformers...")
        try:
            subprocess.run([sys.executable, '-m', 'pip', 'install', 'sentence-transformers'], check=True)
            print("✅ sentence-transformers installed")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install sentence-transformers")
            return False

def test_sentence_transformer():
    """Test if sentence transformer can be loaded"""
    try:
        from sentence_transformers import SentenceTransformer
        print("🧪 Testing sentence transformer model...")
        
        # Try to load the model
        model = SentenceTransformer("all-MiniLM-L6-v2")
        print("✅ Model loaded successfully")
        
        # Test encoding
        embedding = model.encode(["test sentence"])
        print(f"✅ Encoding works, dimension: {len(embedding[0])}")
        
        return True
    except Exception as e:
        print(f"❌ Sentence transformer test failed: {e}")
        return False

def start_retriever_agent():
    """Start the Retriever Agent"""
    print("🚀 Starting Retriever Agent...")
    
    try:
        # Start the process
        process = subprocess.Popen([
            sys.executable, 'agents/Retriever_Agent.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for startup
        print("⏳ Waiting for startup...")
        time.sleep(10)  # Give more time for model loading
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print("❌ Retriever Agent failed to start")
            print("STDOUT:", stdout)
            print("STDERR:", stderr)
            return False
        
        # Test health endpoint
        max_retries = 15
        for i in range(max_retries):
            try:
                response = requests.get("http://localhost:8003/health", timeout=5)
                if response.status_code == 200:
                    health_data = response.json()
                    print("✅ Retriever Agent is healthy")
                    print(f"   Encoder status: {health_data.get('encoder_status', 'unknown')}")
                    print(f"   Index status: {health_data.get('index_status', 'unknown')}")
                    print(f"   Documents: {health_data.get('total_documents', 0)}")
                    return True
                else:
                    print(f"⚠️  Health check returned {response.status_code}")
            except requests.exceptions.RequestException:
                pass
            
            print(f"⏳ Waiting for health check... ({i+1}/{max_retries})")
            time.sleep(2)
        
        print("❌ Health check failed after retries")
        return False
        
    except Exception as e:
        print(f"❌ Error starting Retriever Agent: {e}")
        return False

def test_retriever_functionality():
    """Test basic Retriever Agent functionality"""
    try:
        print("🧪 Testing Retriever Agent functionality...")
        
        # Test adding a document
        response = requests.post(
            "http://localhost:8003/documents",
            params={
                "doc_id": "test_doc_1",
                "content": "Apple Inc. is a technology company",
                "metadata": '{"type": "test"}'
            },
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Document addition works")
            
            # Test search
            search_response = requests.get(
                "http://localhost:8003/search",
                params={"query": "Apple technology", "k": 5},
                timeout=10
            )
            
            if search_response.status_code == 200:
                results = search_response.json()
                print(f"✅ Search works, returned {len(results)} results")
                return True
            else:
                print(f"❌ Search failed: {search_response.status_code}")
        else:
            print(f"❌ Document addition failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
    
    return False

def main():
    """Main function"""
    print("🔧 Fixing Retriever Agent")
    print("=" * 40)
    
    # Step 1: Kill existing process
    print("🔴 Stopping existing Retriever Agent...")
    kill_retriever_agent()
    time.sleep(2)
    
    # Step 2: Check dependencies
    print("\n🔍 Checking dependencies...")
    if not check_dependencies():
        print("❌ Dependency check failed")
        return False
    
    # Step 3: Test sentence transformer
    print("\n🧪 Testing sentence transformer...")
    if not test_sentence_transformer():
        print("❌ Sentence transformer test failed")
        print("Try: pip install --upgrade sentence-transformers")
        return False
    
    # Step 4: Start Retriever Agent
    print("\n🚀 Starting Retriever Agent...")
    if not start_retriever_agent():
        print("❌ Failed to start Retriever Agent")
        return False
    
    # Step 5: Test functionality
    print("\n🧪 Testing functionality...")
    if test_retriever_functionality():
        print("\n🎉 Retriever Agent is working correctly!")
        print("✅ You can now test the full system:")
        print("   curl http://localhost:8000/health")
        return True
    else:
        print("\n⚠️  Retriever Agent started but functionality test failed")
        print("   This might be normal if the encoder is unavailable")
        print("   The agent will work in fallback mode")
        return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Failed to fix Retriever Agent")
        print("Check the error messages above for details")
        sys.exit(1)
