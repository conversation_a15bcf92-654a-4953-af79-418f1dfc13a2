# Local LLM Setup Guide

This guide explains how to set up and use local LLMs (TinyLlama or Llama) with the Finance Assistant.

## 🤖 Supported Models

### TinyLlama (Recommended for M2 Mac)
- **Model**: `TinyLlama/TinyLlama-1.1B-Chat-v1.0`
- **Size**: ~2.2GB
- **RAM Required**: 4-6GB
- **Performance**: Fast inference, good for basic financial analysis

### Llama 3.1 8B (Advanced)
- **Model**: `meta-llama/Llama-3.1-8B-Instruct`
- **Size**: ~16GB
- **RAM Required**: 16-24GB
- **Performance**: High-quality responses, slower inference

## 🚀 Quick Setup

### Option 1: TinyLlama (Recommended)
```bash
# Set in .env file
LOCAL_LLM_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0

# The model will download automatically on first run
# Download size: ~2.2GB
```

### Option 2: Llama 3.1 8B (Advanced Users)
```bash
# Set in .env file
LOCAL_LLM_MODEL=meta-llama/Llama-3.1-8B-Instruct

# Note: Requires Hugging Face account and model access approval
# Download size: ~16GB
```

## 📋 Prerequisites

### System Requirements
- **Apple M2 Mac**: Recommended (MPS acceleration)
- **RAM**: 8GB minimum (16GB recommended for Llama)
- **Storage**: 5GB free space (20GB for Llama)
- **Python**: 3.11+

### Dependencies
```bash
# Core ML dependencies (already in requirements.txt)
torch>=2.0.0
transformers>=4.35.0
```

## 🔧 Configuration

### Environment Variables
```bash
# .env file
LOCAL_LLM_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0
```

### Model Selection
The system automatically detects your hardware and optimizes accordingly:
- **Apple M2**: Uses MPS (Metal Performance Shaders) acceleration
- **Other systems**: Falls back to CPU inference

## 🏃‍♂️ First Run

### Initial Model Download
```bash
# Start the Language Agent
python agents/Language_Agent.py

# Output will show:
# INFO:__main__:Using device: mps
# INFO:__main__:Loading model: TinyLlama/TinyLlama-1.1B-Chat-v1.0
# Downloading model files... (this may take a few minutes)
# INFO:__main__:Local LLM loaded successfully
```

### Model Storage
Models are cached in:
```bash
~/.cache/huggingface/transformers/
```

## 🎯 Performance Optimization

### TinyLlama Optimization
```python
# Automatic optimizations applied:
- torch_dtype=torch.float16  # Half precision
- device_map="auto"          # Automatic device mapping
- max_new_tokens=512         # Reasonable response length
- temperature=0.3            # Focused responses
```

### Memory Management
```bash
# Monitor memory usage
htop  # or Activity Monitor on Mac

# Expected memory usage:
# TinyLlama: ~3-4GB RAM
# Llama 8B: ~12-16GB RAM
```

## 🧪 Testing Local LLM

### Test Language Agent
```bash
# Test the language agent directly
curl -X POST "http://localhost:8005/analyze" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "Analyze Apple stock performance",
    "context_data": {"market": {"AAPL": {"price": 150}}},
    "analysis_type": "market_brief"
  }'
```

### Expected Response
```json
{
  "query": "Analyze Apple stock performance",
  "analysis": "Based on the current market data, Apple (AAPL) is trading at $150...",
  "confidence": 0.75,
  "sources": ["Local LLM Analysis", "Market Data"],
  "timestamp": "2024-01-01T12:00:00"
}
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Model Download Fails
```bash
# Error: Connection timeout
# Solution: Check internet connection and retry
# The download will resume from where it left off
```

#### 2. Out of Memory Error
```bash
# Error: CUDA out of memory / MPS out of memory
# Solution: Use TinyLlama instead of Llama 8B
LOCAL_LLM_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0
```

#### 3. Slow Inference
```bash
# Issue: Responses take too long
# Solution: Reduce max_new_tokens in Language_Agent.py
max_new_tokens=256  # Instead of 512
```

#### 4. Model Not Found
```bash
# Error: Model not found
# Solution: Check model name spelling
# Verify internet connection for download
```

### Performance Tuning

#### For Better Speed
```python
# In Language_Agent.py, modify:
max_new_tokens=256      # Shorter responses
temperature=0.1         # More focused
do_sample=False         # Deterministic
```

#### For Better Quality
```python
# In Language_Agent.py, modify:
max_new_tokens=512      # Longer responses
temperature=0.5         # More creative
do_sample=True          # Sampling enabled
```

## 📊 Model Comparison

| Model | Size | RAM | Speed | Quality | Use Case |
|-------|------|-----|-------|---------|----------|
| TinyLlama | 2.2GB | 4-6GB | Fast | Good | Development, Testing |
| Llama 8B | 16GB | 16-24GB | Slow | Excellent | Production, Research |

## 🔄 Switching Models

### Change Model
```bash
# Edit .env file
LOCAL_LLM_MODEL=meta-llama/Llama-3.1-8B-Instruct

# Restart Language Agent
# New model will download automatically
```

### Fallback Behavior
If the local LLM fails to load:
1. System provides basic text analysis
2. Error messages are logged
3. Other agents continue to function
4. Fallback responses are generated

## 🎓 Advanced Usage

### Custom Model Integration
```python
# To use a different model, modify Language_Agent.py:
self.model_name = "your-custom-model-name"

# Ensure the model supports chat format or adjust prompts accordingly
```

### Prompt Engineering
```python
# Customize prompts in Language_Agent.py:
chat_prompt = f"""<|system|>
You are a professional financial analyst specializing in {domain}.
<|user|>
{prompt}
<|assistant|>
"""
```

## 📈 Monitoring

### Performance Metrics
- Response time: 2-10 seconds (TinyLlama)
- Memory usage: 3-4GB (TinyLlama)
- Model loading time: 30-60 seconds (first run)

### Logging
```bash
# Check logs for model performance
tail -f logs/language_agent.log

# Look for:
# - Model loading success/failure
# - Response generation times
# - Memory usage warnings
```

---

**For additional support with local LLM setup, please check the main README.md or create an issue.**
