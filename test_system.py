#!/usr/bin/env python3
"""
System Test Script for Multi-Agent Finance Assistant
Tests basic functionality of all agents and the orchestrator
"""

import requests
import time
import json
from typing import Dict, List

# Configuration
ORCHESTRATOR_URL = "http://localhost:8000"
AGENT_URLS = {
    "api": "http://localhost:8001",
    "financial_data": "http://localhost:8002",
    "retriever": "http://localhost:8003",
    "analysis": "http://localhost:8004",
    "language": "http://localhost:8005",
    "text_processing": "http://localhost:8006"
}

def test_health_check(url: str, service_name: str) -> bool:
    """Test health check endpoint"""
    try:
        response = requests.get(f"{url}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ {service_name} is healthy")
            return True
        else:
            print(f"❌ {service_name} health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {service_name} is not responding: {e}")
        return False

def test_api_agent() -> bool:
    """Test API Agent functionality"""
    print("\n🧪 Testing API Agent...")
    try:
        # Test stock summary
        response = requests.get(f"{AGENT_URLS['api']}/stock-summary?symbol=AAPL", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Stock summary for AAPL: ${data.get('current_price', 'N/A')}")
            return True
        else:
            print(f"❌ API Agent test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Agent test error: {e}")
        return False

def test_financial_data_agent() -> bool:
    """Test Financial Data Agent functionality"""
    print("\n🧪 Testing Financial Data Agent...")
    try:
        # Test news from Financial Modeling Prep
        response = requests.get(f"{AGENT_URLS['financial_data']}/news?symbol=TSLA&limit=3", timeout=15)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Retrieved {len(data)} news articles for TSLA")
            return True
        else:
            print(f"❌ Financial Data Agent test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Financial Data Agent test error: {e}")
        return False

def test_retriever_agent() -> bool:
    """Test Retriever Agent functionality"""
    print("\n🧪 Testing Retriever Agent...")
    try:
        # Test adding a document
        response = requests.post(
            f"{AGENT_URLS['retriever']}/documents",
            params={
                "doc_id": "test_doc_1",
                "content": "Apple Inc. is a technology company that designs and manufactures consumer electronics.",
                "metadata": '{"type": "test", "symbol": "AAPL"}'
            },
            timeout=10
        )

        if response.status_code == 200:
            print("✅ Document added successfully")

            # Test search
            search_response = requests.get(
                f"{AGENT_URLS['retriever']}/search",
                params={"query": "Apple technology company", "k": 5},
                timeout=10
            )

            if search_response.status_code == 200:
                results = search_response.json()
                print(f"✅ Search returned {len(results)} results")
                return True
            else:
                print(f"❌ Search test failed: {search_response.status_code}")
                return False
        else:
            print(f"❌ Document addition failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Retriever Agent test error: {e}")
        return False

def test_analysis_agent() -> bool:
    """Test Analysis Agent functionality"""
    print("\n🧪 Testing Analysis Agent...")
    try:
        # Test risk metrics
        response = requests.get(f"{AGENT_URLS['analysis']}/risk-metrics/AAPL", timeout=15)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Risk metrics for AAPL - Volatility: {data.get('volatility', 'N/A')}")
            return True
        else:
            print(f"❌ Analysis Agent test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Analysis Agent test error: {e}")
        return False

def test_language_agent() -> bool:
    """Test Language Agent functionality"""
    print("\n🧪 Testing Language Agent...")
    try:
        # Test capabilities endpoint
        response = requests.get(f"{AGENT_URLS['language']}/capabilities", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Language Agent capabilities: {len(data.get('analysis_types', []))} analysis types")
            return True
        else:
            print(f"❌ Language Agent test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Language Agent test error: {e}")
        return False

def test_text_processing_agent() -> bool:
    """Test Text Processing Agent functionality"""
    print("\n🧪 Testing Text Processing Agent...")
    try:
        # Test text analysis
        response = requests.get(f"{AGENT_URLS['text_processing']}/analyze-text?text=Apple stock gained 5% today", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Text Processing Agent analyzed text successfully")
            return True
        else:
            print(f"❌ Text Processing Agent test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Text Processing Agent test error: {e}")
        return False

def test_orchestrator() -> bool:
    """Test Orchestrator functionality"""
    print("\n🧪 Testing Orchestrator...")
    try:
        # Test market brief generation
        response = requests.post(
            f"{ORCHESTRATOR_URL}/market-brief",
            json={
                "query": "What's the current market outlook?",
                "symbols": ["AAPL", "GOOGL"],
                "include_voice": False
            },
            timeout=30
        )

        if response.status_code == 200:
            data = response.json()
            print("✅ Market brief generated successfully")
            return True
        else:
            print(f"❌ Orchestrator test failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Orchestrator test error: {e}")
        return False

def main():
    """Run all system tests"""
    print("🚀 Starting Multi-Agent Finance Assistant System Tests")
    print("=" * 60)

    # Wait for services to be ready
    print("⏳ Waiting for services to start...")
    time.sleep(5)

    # Test health checks
    print("\n🔍 Testing Health Checks...")
    health_results = []

    # Test orchestrator health
    health_results.append(test_health_check(ORCHESTRATOR_URL, "Orchestrator"))

    # Test all agents
    for agent_name, url in AGENT_URLS.items():
        health_results.append(test_health_check(url, f"{agent_name.title()} Agent"))

    # If any health checks failed, exit
    if not all(health_results):
        print("\n❌ Some services are not healthy. Please check the logs and try again.")
        return False

    # Test individual agent functionality
    test_results = []

    test_results.append(test_api_agent())
    test_results.append(test_financial_data_agent())
    test_results.append(test_retriever_agent())
    test_results.append(test_analysis_agent())
    test_results.append(test_language_agent())
    test_results.append(test_text_processing_agent())

    # Test orchestrator
    test_results.append(test_orchestrator())

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print(f"✅ Passed: {sum(test_results)}")
    print(f"❌ Failed: {len(test_results) - sum(test_results)}")

    if all(test_results):
        print("\n🎉 All tests passed! The system is working correctly.")
        print("\n🌐 You can now access:")
        print("   • Streamlit UI: http://localhost:8501")
        print("   • API Documentation: http://localhost:8000/docs")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the logs for more details.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
