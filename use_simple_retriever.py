#!/usr/bin/env python3
"""
Switch to the simple retriever agent (no sentence-transformers dependency)
"""

import subprocess
import time
import requests
import sys
import shutil
from pathlib import Path

def backup_original():
    """Backup the original retriever agent"""
    original = Path("agents/Retriever_Agent.py")
    backup = Path("agents/Retriever_Agent_Original.py")
    
    if original.exists() and not backup.exists():
        shutil.copy(original, backup)
        print("✅ Original Retriever Agent backed up")

def switch_to_simple():
    """Switch to the simple retriever agent"""
    simple = Path("agents/Retriever_Agent_Simple.py")
    target = Path("agents/Retriever_Agent.py")
    
    if simple.exists():
        shutil.copy(simple, target)
        print("✅ Switched to Simple Retriever Agent")
        return True
    else:
        print("❌ Simple Retriever Agent not found")
        return False

def kill_retriever_process():
    """Kill any existing retriever process"""
    try:
        result = subprocess.run(['lsof', '-ti', ':8003'], capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            pids = result.stdout.strip().split('\n')
            for pid in pids:
                try:
                    subprocess.run(['kill', pid], check=True)
                    print(f"🔴 Killed process {pid} on port 8003")
                except subprocess.CalledProcessError:
                    subprocess.run(['kill', '-9', pid], check=True)
                    print(f"🔴 Force killed process {pid}")
    except Exception as e:
        print(f"⚠️  Error killing processes: {e}")

def start_simple_retriever():
    """Start the simple retriever agent"""
    print("🚀 Starting Simple Retriever Agent...")
    
    try:
        process = subprocess.Popen([
            sys.executable, 'agents/Retriever_Agent.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Wait for startup
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print("❌ Simple Retriever Agent failed to start")
            print("STDERR:", stderr)
            return False
        
        # Test health endpoint
        max_retries = 10
        for i in range(max_retries):
            try:
                response = requests.get("http://localhost:8003/health", timeout=5)
                if response.status_code == 200:
                    health_data = response.json()
                    print("✅ Simple Retriever Agent is healthy")
                    print(f"   Type: {health_data.get('type', 'unknown')}")
                    print(f"   Documents: {health_data.get('total_documents', 0)}")
                    return True
            except requests.exceptions.RequestException:
                pass
            
            print(f"⏳ Waiting for health check... ({i+1}/{max_retries})")
            time.sleep(1)
        
        print("❌ Health check failed")
        return False
        
    except Exception as e:
        print(f"❌ Error starting Simple Retriever Agent: {e}")
        return False

def test_simple_retriever():
    """Test the simple retriever functionality"""
    try:
        print("🧪 Testing Simple Retriever functionality...")
        
        # Test adding a document
        response = requests.post(
            "http://localhost:8003/documents",
            params={
                "doc_id": "test_simple_1",
                "content": "Apple Inc. is a technology company that makes iPhones and computers",
                "metadata": '{"type": "test", "company": "Apple"}'
            },
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Document addition works")
            
            # Test search
            search_response = requests.get(
                "http://localhost:8003/search",
                params={"query": "Apple technology iPhone", "k": 5},
                timeout=10
            )
            
            if search_response.status_code == 200:
                results = search_response.json()
                print(f"✅ Search works, returned {len(results)} results")
                if results:
                    print(f"   Best match score: {results[0]['score']:.3f}")
                return True
            else:
                print(f"❌ Search failed: {search_response.status_code}")
        else:
            print(f"❌ Document addition failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Functionality test failed: {e}")
    
    return False

def main():
    """Main function"""
    print("🔄 Switching to Simple Retriever Agent")
    print("=" * 50)
    print("This will replace the sentence-transformers based retriever")
    print("with a simple keyword-based search system.")
    print()
    
    # Step 1: Backup original
    print("📁 Backing up original...")
    backup_original()
    
    # Step 2: Kill existing process
    print("\n🔴 Stopping existing Retriever Agent...")
    kill_retriever_process()
    time.sleep(2)
    
    # Step 3: Switch to simple version
    print("\n🔄 Switching to Simple Retriever...")
    if not switch_to_simple():
        print("❌ Failed to switch to simple retriever")
        return False
    
    # Step 4: Start simple retriever
    print("\n🚀 Starting Simple Retriever Agent...")
    if not start_simple_retriever():
        print("❌ Failed to start Simple Retriever Agent")
        return False
    
    # Step 5: Test functionality
    print("\n🧪 Testing functionality...")
    if test_simple_retriever():
        print("\n🎉 Simple Retriever Agent is working!")
        print("\n✅ Benefits of Simple Retriever:")
        print("   • No sentence-transformers dependency")
        print("   • Fast keyword-based search")
        print("   • TF-IDF scoring")
        print("   • Lower memory usage")
        print("\n📊 Test the full system:")
        print("   curl http://localhost:8000/health")
        return True
    else:
        print("\n⚠️  Simple Retriever started but functionality test failed")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Failed to switch to Simple Retriever")
        sys.exit(1)
    else:
        print("\n🎉 Successfully switched to Simple Retriever Agent!")
        print("Your system should now be fully functional.")
