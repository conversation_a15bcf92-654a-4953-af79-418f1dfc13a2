version: '3.8'

services:
  finance-assistant:
    build: .
    ports:
      - "8000:8000"
      - "8501:8501"
    environment:
      - PYTHONPATH=/app
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./.env:/app/.env
    command: >
      sh -c "uvicorn orchestrator.main:app --host 0.0.0.0 --port 8000 &
             streamlit run streamlit_app/main.py --server.port 8501 --server.address 0.0.0.0"
    
  # Optional: Add a separate service for each agent if needed
  # api-agent:
  #   build: .
  #   ports:
  #     - "8001:8001"
  #   command: uvicorn agents.API_Agent:app --host 0.0.0.0 --port 8001
