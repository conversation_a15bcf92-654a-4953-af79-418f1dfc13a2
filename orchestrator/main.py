from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Query, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import List, Dict, Optional, Any
from pydantic import BaseModel
import httpx
import logging
from datetime import datetime
import json
import asyncio
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Finance Assistant Orchestrator",
    description="Multi-Agent Finance Assistant Orchestrator",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Agent service URLs
AGENT_URLS = {
    "api": "http://localhost:8001",
    "financial_data": "http://localhost:8002",  # Renamed from scraping
    "retriever": "http://localhost:8003",
    "analysis": "http://localhost:8004",
    "language": "http://localhost:8005",
    "text_processing": "http://localhost:8006"  # Renamed from voice
}

class MarketBriefRequest(BaseModel):
    query: str
    symbols: List[str] = ["TSLA", "AAPL", "GOOGL"]
    include_voice: bool = False

class PortfolioPosition(BaseModel):
    symbol: str
    shares: float
    avg_cost: float

class PortfolioAnalysisRequest(BaseModel):
    positions: List[PortfolioPosition]
    query: str = "Analyze my portfolio risk exposure"

class Orchestrator:
    def __init__(self):
        self.confidence_threshold = float(os.getenv("CONFIDENCE_THRESHOLD", "0.7"))

    async def call_agent(self, agent: str, endpoint: str, method: str = "GET", **kwargs) -> Dict:
        """Call a specific agent endpoint"""
        try:
            url = f"{AGENT_URLS[agent]}{endpoint}"

            async with httpx.AsyncClient(timeout=30.0) as client:
                if method == "GET":
                    response = await client.get(url, params=kwargs.get('params', {}))
                elif method == "POST":
                    response = await client.post(
                        url,
                        json=kwargs.get('json', {}),
                        params=kwargs.get('params', {})
                    )
                else:
                    raise ValueError(f"Unsupported method: {method}")

                response.raise_for_status()
                return response.json()

        except httpx.RequestError as e:
            logger.error(f"Error calling {agent} agent: {e}")
            return {"error": f"Failed to connect to {agent} agent"}
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error from {agent} agent: {e}")
            return {"error": f"HTTP {e.response.status_code} from {agent} agent"}
        except Exception as e:
            logger.error(f"Unexpected error calling {agent} agent: {e}")
            return {"error": f"Unexpected error: {str(e)}"}

    async def gather_market_data(self, symbols: List[str]) -> Dict:
        """Gather comprehensive market data"""
        tasks = []

        # Get stock summaries
        for symbol in symbols:
            tasks.append(self.call_agent("api", "/stock-summary", params={"symbol": symbol}))

        # Get market movers
        tasks.append(self.call_agent("api", "/market-movers"))

        # Get news for each symbol
        for symbol in symbols:
            tasks.append(self.call_agent("financial_data", "/news", params={"symbol": symbol, "limit": 5}))

        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        market_data = {
            "stock_summaries": {},
            "market_movers": {},
            "news": {}
        }

        # Parse stock summaries
        for i, symbol in enumerate(symbols):
            if i < len(results) and not isinstance(results[i], Exception):
                market_data["stock_summaries"][symbol] = results[i]

        # Parse market movers
        movers_index = len(symbols)
        if movers_index < len(results) and not isinstance(results[movers_index], Exception):
            market_data["market_movers"] = results[movers_index]

        # Parse news
        for i, symbol in enumerate(symbols):
            news_index = len(symbols) + 1 + i
            if news_index < len(results) and not isinstance(results[news_index], Exception):
                market_data["news"][symbol] = results[news_index]

        return market_data

    async def generate_market_brief(self, query: str, symbols: List[str]) -> Dict:
        """Generate comprehensive market brief"""
        try:
            # Step 1: Gather data from multiple sources
            market_data = await self.gather_market_data(symbols)

            # Step 2: Get risk analysis for symbols
            risk_tasks = [
                self.call_agent("analysis", f"/risk-metrics/{symbol}")
                for symbol in symbols
            ]
            risk_results = await asyncio.gather(*risk_tasks, return_exceptions=True)

            risk_data = {}
            for i, symbol in enumerate(symbols):
                if i < len(risk_results) and not isinstance(risk_results[i], Exception):
                    risk_data[symbol] = risk_results[i]

            # Step 3: Get market sentiment
            sentiment_result = await self.call_agent(
                "financial_data",
                "/market-sentiment",
                params={"query": "market outlook", "limit": 20}
            )

            # Step 4: Prepare context for language agent
            context_data = {
                "market": market_data,
                "risk_metrics": risk_data,
                "sentiment": sentiment_result,
                "symbols": symbols
            }

            # Step 5: Generate analysis using language agent
            analysis_result = await self.call_agent(
                "language",
                "/market-brief",
                method="POST",
                params={
                    "query": query,
                    "portfolio_data": json.dumps({}),
                    "market_data": json.dumps(market_data),
                    "news_data": json.dumps(market_data.get("news", {}))
                }
            )

            return {
                "query": query,
                "symbols": symbols,
                "market_data": market_data,
                "risk_analysis": risk_data,
                "sentiment": sentiment_result,
                "analysis": analysis_result,
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error generating market brief: {e}")
            raise HTTPException(status_code=500, detail="Failed to generate market brief")

orchestrator = Orchestrator()

@app.get("/health")
async def health_check():
    """Health check for orchestrator and all agents"""
    health_status = {"orchestrator": "healthy", "agents": {}}

    # Check each agent
    for agent_name, base_url in AGENT_URLS.items():
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get(f"{base_url}/health")
                health_status["agents"][agent_name] = response.json()
        except Exception as e:
            health_status["agents"][agent_name] = {"status": "unhealthy", "error": str(e)}

    return health_status

@app.post("/market-brief")
async def generate_market_brief(request: MarketBriefRequest):
    """Generate comprehensive market brief"""
    try:
        result = await orchestrator.generate_market_brief(request.query, request.symbols)

        # If voice output is requested, generate audio
        if request.include_voice and "analysis" in result:
            analysis_text = result["analysis"].get("analysis", "")
            if analysis_text:
                voice_result = await orchestrator.call_agent(
                    "voice",
                    "/text-to-speech",
                    params={"text": analysis_text, "language": "en"}
                )
                result["voice_response"] = voice_result

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in market brief generation: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/portfolio-analysis")
async def analyze_portfolio(request: PortfolioAnalysisRequest):
    """Analyze portfolio with comprehensive risk assessment"""
    try:
        # Step 1: Get portfolio analysis
        portfolio_result = await orchestrator.call_agent(
            "analysis",
            "/portfolio-analysis",
            method="POST",
            json=[pos.dict() for pos in request.positions]
        )

        # Step 2: Get news for portfolio symbols
        symbols = [pos.symbol for pos in request.positions]
        news_tasks = [
            orchestrator.call_agent("financial_data", "/news", params={"symbol": symbol, "limit": 3})
            for symbol in symbols
        ]
        news_results = await asyncio.gather(*news_tasks, return_exceptions=True)

        portfolio_news = {}
        for i, symbol in enumerate(symbols):
            if i < len(news_results) and not isinstance(news_results[i], Exception):
                portfolio_news[symbol] = news_results[i]

        # Step 3: Generate insights using language agent
        context_data = {
            "portfolio": portfolio_result,
            "news": portfolio_news,
            "symbols": symbols
        }

        analysis_result = await orchestrator.call_agent(
            "language",
            "/analyze",
            method="POST",
            json={
                "query": request.query,
                "context_data": context_data,
                "analysis_type": "risk"
            }
        )

        return {
            "query": request.query,
            "portfolio_analysis": portfolio_result,
            "news": portfolio_news,
            "insights": analysis_result,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in portfolio analysis: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")

@app.post("/text-query")
async def process_text_query(
    query: str = Query(..., description="Text query to process"),
    operation: str = Query("analyze", description="Text operation: analyze, summarize, format")
):
    """Process text query using text processing agent"""
    try:
        # Step 1: Process the text query
        text_result = await orchestrator.call_agent(
            "text_processing",
            "/process-text",
            method="POST",
            json={"text": query, "operation": operation}
        )

        if "error" in text_result:
            raise HTTPException(status_code=400, detail="Failed to process text")

        # Step 2: Determine query type and route appropriately
        if any(word in query.lower() for word in ["portfolio", "risk", "exposure"]):
            # Portfolio-related query - would need portfolio data
            response_text = "I understand you're asking about portfolio analysis. Please provide your portfolio positions for a detailed analysis."
        elif any(word in query.lower() for word in ["market", "stock", "price"]):
            # Market-related query
            symbols = ["AAPL", "GOOGL", "TSLA"]  # Default symbols
            brief_result = await orchestrator.generate_market_brief(query, symbols)
            response_text = brief_result.get("analysis", {}).get("analysis", "Market analysis completed.")
        else:
            response_text = f"Query processed: '{query}'. I'm a financial assistant. Please ask about market conditions, portfolio analysis, or stock information."

        return {
            "original_query": query,
            "text_analysis": text_result,
            "response_text": response_text,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing text query: {e}")
        raise HTTPException(status_code=500, detail="Text processing failed")

@app.get("/agents/status")
async def get_agents_status():
    """Get detailed status of all agents"""
    status = {}

    for agent_name, base_url in AGENT_URLS.items():
        try:
            async with httpx.AsyncClient(timeout=5.0) as client:
                health_response = await client.get(f"{base_url}/health")
                capabilities_response = await client.get(f"{base_url}/capabilities")

                status[agent_name] = {
                    "health": health_response.json(),
                    "capabilities": capabilities_response.json(),
                    "url": base_url
                }
        except Exception as e:
            status[agent_name] = {
                "health": {"status": "unhealthy", "error": str(e)},
                "capabilities": {},
                "url": base_url
            }

    return status

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
