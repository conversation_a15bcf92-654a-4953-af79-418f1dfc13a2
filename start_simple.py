#!/usr/bin/env python3
"""
Simple startup script for Multi-Agent Finance Assistant
Starts all agents with proper error handling and health checks
"""

import subprocess
import time
import requests
import sys
import os
from pathlib import Path

# Agent configurations
AGENTS = [
    {"name": "API Agent", "file": "agents/API_Agent.py", "port": 8001},
    {"name": "Financial Data Agent", "file": "agents/Scraping_Agent.py", "port": 8002},
    {"name": "Retriever Agent", "file": "agents/Retriever_Agent.py", "port": 8003},
    {"name": "Analysis Agent", "file": "agents/Analysis_Agent.py", "port": 8004},
    {"name": "Language Agent", "file": "agents/Language_Agent.py", "port": 8005},
    {"name": "Text Processing Agent", "file": "agents/Voice_Agent.py", "port": 8006},
]

ORCHESTRATOR = {"name": "Orchestrator", "file": "orchestrator/main.py", "port": 8000}
STREAMLIT = {"name": "Streamlit", "file": "streamlit_app/main.py", "port": 8501}

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ is required")
        sys.exit(1)
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")

def check_dependencies():
    """Check if required packages are installed"""
    required_packages = ['fastapi', 'uvicorn', 'streamlit', 'torch', 'transformers']
    missing = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing.append(package)
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        sys.exit(1)
    print("✅ All required packages found")

def create_directories():
    """Create necessary directories"""
    dirs = ['data/faiss_index', 'logs', 'data/raw', 'data/processed']
    for dir_path in dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    print("✅ Directories created")

def check_env_file():
    """Check if .env file exists"""
    if not Path('.env').exists():
        print("⚠️  .env file not found. Creating from template...")
        if Path('.env.template').exists():
            import shutil
            shutil.copy('.env.template', '.env')
            print("✅ .env file created from template")
            print("📝 Please edit .env file with your API keys")
        else:
            print("❌ .env.template not found")
            sys.exit(1)
    else:
        print("✅ .env file found")

def start_agent(agent_config):
    """Start a single agent"""
    print(f"🚀 Starting {agent_config['name']}...")
    
    try:
        # Start the agent process
        process = subprocess.Popen([
            sys.executable, agent_config['file']
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Wait a bit for the agent to start
        time.sleep(3)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            print(f"❌ {agent_config['name']} failed to start")
            print(f"Error: {stderr.decode()}")
            return None
        
        # Check health endpoint
        max_retries = 10
        for i in range(max_retries):
            try:
                response = requests.get(f"http://localhost:{agent_config['port']}/health", timeout=2)
                if response.status_code == 200:
                    print(f"✅ {agent_config['name']} is healthy")
                    return process
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        print(f"⚠️  {agent_config['name']} started but health check failed")
        return process
        
    except Exception as e:
        print(f"❌ Error starting {agent_config['name']}: {e}")
        return None

def start_orchestrator():
    """Start the orchestrator"""
    print("🎯 Starting Orchestrator...")
    
    try:
        process = subprocess.Popen([
            sys.executable, ORCHESTRATOR['file']
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(5)  # Orchestrator needs more time
        
        # Check health
        max_retries = 15
        for i in range(max_retries):
            try:
                response = requests.get(f"http://localhost:{ORCHESTRATOR['port']}/health", timeout=2)
                if response.status_code == 200:
                    print("✅ Orchestrator is healthy")
                    return process
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        print("⚠️  Orchestrator started but health check failed")
        return process
        
    except Exception as e:
        print(f"❌ Error starting Orchestrator: {e}")
        return None

def start_streamlit():
    """Start Streamlit"""
    print("🌐 Starting Streamlit...")
    
    try:
        process = subprocess.Popen([
            sys.executable, "-m", "streamlit", "run", STREAMLIT['file'],
            "--server.port", str(STREAMLIT['port']),
            "--server.address", "0.0.0.0"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        time.sleep(5)
        
        # Check if Streamlit is accessible
        max_retries = 10
        for i in range(max_retries):
            try:
                response = requests.get(f"http://localhost:{STREAMLIT['port']}", timeout=2)
                if response.status_code == 200:
                    print("✅ Streamlit is running")
                    return process
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
        
        print("⚠️  Streamlit started but not accessible")
        return process
        
    except Exception as e:
        print(f"❌ Error starting Streamlit: {e}")
        return None

def main():
    """Main startup function"""
    print("🚀 Starting Multi-Agent Finance Assistant")
    print("=" * 50)
    
    # Pre-flight checks
    check_python_version()
    check_dependencies()
    create_directories()
    check_env_file()
    
    print("\n🔄 Starting services...")
    
    # Start agents
    agent_processes = []
    for agent in AGENTS:
        process = start_agent(agent)
        if process:
            agent_processes.append((agent['name'], process))
        else:
            print(f"⚠️  Continuing without {agent['name']}")
    
    # Start orchestrator
    orchestrator_process = start_orchestrator()
    
    # Start Streamlit
    streamlit_process = start_streamlit()
    
    print("\n" + "=" * 50)
    print("🎉 Startup complete!")
    print("\n📊 Access Points:")
    print("   • Streamlit UI: http://localhost:8501")
    print("   • Orchestrator API: http://localhost:8000")
    print("   • API Documentation: http://localhost:8000/docs")
    
    print("\n🔍 Health Check:")
    print("   curl http://localhost:8000/health")
    
    print("\n📝 Logs:")
    print("   Check terminal output for any errors")
    
    print("\n🛑 To stop:")
    print("   Press Ctrl+C or run: python stop_simple.py")
    
    # Keep the script running
    try:
        print("\n⏳ Services running... Press Ctrl+C to stop")
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Stopping services...")
        
        # Stop all processes
        all_processes = agent_processes + [
            ("Orchestrator", orchestrator_process),
            ("Streamlit", streamlit_process)
        ]
        
        for name, process in all_processes:
            if process:
                print(f"🔴 Stopping {name}...")
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
        
        print("✅ All services stopped")

if __name__ == "__main__":
    main()
