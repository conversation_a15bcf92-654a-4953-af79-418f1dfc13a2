# 🚀 Deployment Guide - Multi-Agent Finance Assistant

This guide provides step-by-step instructions for deploying the Multi-Agent Finance Assistant system.

## 📋 Prerequisites

### System Requirements
- **Python**: 3.11 or higher
- **Memory**: Minimum 4GB RAM (8GB recommended)
- **Storage**: 2GB free space
- **Network**: Internet connection for API calls

### Required API Keys
1. **Alpha Vantage API Key** (Free tier available)
   - Sign up at: https://www.alphavantage.co/support/#api-key
   - Free tier: 5 API requests per minute, 500 requests per day

2. **OpenAI API Key** (Optional, for enhanced LLM features)
   - Sign up at: https://platform.openai.com/api-keys
   - Pay-per-use pricing

## 🛠️ Local Development Setup

### Step 1: Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd RagaAI-Assignment

# Create virtual environment
python -m venv RAGA
source RAGA/bin/activate  # On Windows: RAGA\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Step 2: Environment Configuration
```bash
# Copy environment template
cp .env.template .env

# Edit .env file with your API keys
nano .env  # or use your preferred editor
```

Required environment variables:
```bash
ALPHA_VANTAGE_API=your_alpha_vantage_api_key_here
OPENAI_API_KEY=your_openai_api_key_here  # Optional
VECTOR_DB_TYPE=faiss
FAISS_INDEX_PATH=./data/faiss_index
CONFIDENCE_THRESHOLD=0.7
```

### Step 3: Create Directories
```bash
mkdir -p data/faiss_index
mkdir -p logs
mkdir -p data/raw
mkdir -p data/processed
```

### Step 4: Start the System
```bash
# Option 1: Use startup script (Recommended)
./start_agents.sh

# Option 2: Manual startup (for debugging)
# Start each agent in separate terminals:
python agents/API_Agent.py          # Terminal 1
python agents/Scraping_Agent.py     # Terminal 2
python agents/Retriever_Agent.py    # Terminal 3
python agents/Analysis_Agent.py     # Terminal 4
python agents/Language_Agent.py     # Terminal 5
python agents/Voice_Agent.py        # Terminal 6
python orchestrator/main.py         # Terminal 7
streamlit run streamlit_app/main.py # Terminal 8
```

### Step 5: Test the System
```bash
# Run system tests
python test_system.py

# Manual health check
curl http://localhost:8000/health
```

### Step 6: Access the Application
- **Streamlit UI**: http://localhost:8501
- **API Documentation**: http://localhost:8000/docs
- **Orchestrator**: http://localhost:8000

## 🐳 Docker Deployment

### Using Docker Compose (Recommended)
```bash
# Build and start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Manual Docker Build
```bash
# Build the image
docker build -t finance-assistant .

# Run the container
docker run -p 8000:8000 -p 8501:8501 \
  -e ALPHA_VANTAGE_API=your_key \
  -v $(pwd)/data:/app/data \
  finance-assistant
```

## ☁️ Cloud Deployment

### Streamlit Cloud (UI Only)
1. Fork the repository to your GitHub account
2. Go to https://share.streamlit.io/
3. Connect your GitHub repository
4. Set the main file path: `streamlit_app/main.py`
5. Add secrets in Streamlit Cloud dashboard:
   ```toml
   ALPHA_VANTAGE_API = "your_key"
   OPENAI_API_KEY = "your_key"
   ```

### Heroku Deployment (Full Stack)
```bash
# Install Heroku CLI
# Create Heroku app
heroku create your-finance-assistant

# Set environment variables
heroku config:set ALPHA_VANTAGE_API=your_key
heroku config:set OPENAI_API_KEY=your_key

# Deploy
git push heroku main
```

### Railway Deployment
1. Connect your GitHub repository to Railway
2. Set environment variables in Railway dashboard
3. Deploy automatically on git push

### AWS/GCP Deployment
For production deployment on AWS or GCP:

1. **Container Registry**: Push Docker image to ECR/GCR
2. **Container Service**: Deploy using ECS/Cloud Run
3. **Load Balancer**: Set up ALB/Cloud Load Balancer
4. **Database**: Use RDS/Cloud SQL for persistent storage
5. **Monitoring**: Set up CloudWatch/Cloud Monitoring

## 🔧 Production Configuration

### Environment Variables for Production
```bash
# Production settings
LOG_LEVEL=INFO
REQUEST_TIMEOUT=60
MAX_RETRIES=5
CONFIDENCE_THRESHOLD=0.8

# Database (if using external vector DB)
PINECONE_API_KEY=your_pinecone_key
PINECONE_ENVIRONMENT=your_environment
VECTOR_DB_TYPE=pinecone

# Security
API_KEY_REQUIRED=true
ALLOWED_ORIGINS=https://yourdomain.com
```

### Performance Optimization
```bash
# Increase worker processes
WORKERS=4
WORKER_CONNECTIONS=1000

# Cache settings
CACHE_TTL=300
ENABLE_CACHING=true

# Rate limiting
RATE_LIMIT_PER_MINUTE=100
```

### Security Hardening
1. **API Authentication**: Implement API key authentication
2. **HTTPS**: Use SSL certificates
3. **CORS**: Configure proper CORS settings
4. **Input Validation**: Validate all inputs
5. **Rate Limiting**: Implement rate limiting

## 📊 Monitoring and Logging

### Log Configuration
```python
# Production logging setup
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s [%(levelname)s] %(name)s: %(message)s'
        },
    },
    'handlers': {
        'default': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.StreamHandler',
        },
        'file': {
            'level': 'INFO',
            'formatter': 'standard',
            'class': 'logging.FileHandler',
            'filename': 'logs/app.log',
        },
    },
    'loggers': {
        '': {
            'handlers': ['default', 'file'],
            'level': 'INFO',
            'propagate': False
        }
    }
}
```

### Health Monitoring
Set up monitoring for:
- Agent availability
- Response times
- Error rates
- Resource usage
- API rate limits

### Alerting
Configure alerts for:
- Service downtime
- High error rates
- API quota exhaustion
- Performance degradation

## 🔄 Scaling Considerations

### Horizontal Scaling
- Deploy multiple instances behind a load balancer
- Use container orchestration (Kubernetes)
- Implement service discovery

### Vertical Scaling
- Increase CPU/memory for compute-intensive agents
- Use GPU instances for ML workloads
- Optimize database performance

### Microservice Scaling
- Scale individual agents based on load
- Use auto-scaling groups
- Implement circuit breakers

## 🛡️ Backup and Recovery

### Data Backup
```bash
# Backup vector index
tar -czf backup_$(date +%Y%m%d).tar.gz data/

# Backup configuration
cp .env .env.backup
```

### Disaster Recovery
1. **Database Backups**: Regular automated backups
2. **Configuration Management**: Version control for configs
3. **Container Images**: Store in multiple registries
4. **Documentation**: Keep deployment docs updated

## 🧪 Testing in Production

### Health Checks
```bash
# Automated health monitoring
curl -f http://your-domain/health || exit 1
```

### Load Testing
```bash
# Use tools like Apache Bench or Artillery
ab -n 1000 -c 10 http://your-domain/market-brief
```

### Integration Testing
- Test all agent interactions
- Validate data flow
- Check error handling
- Verify performance metrics

## 📞 Support and Troubleshooting

### Common Issues
1. **Port Conflicts**: Check if ports 8000-8006, 8501 are available
2. **API Limits**: Monitor API usage and implement caching
3. **Memory Issues**: Increase container memory limits
4. **Network Timeouts**: Adjust timeout settings

### Debug Mode
```bash
# Enable debug logging
export LOG_LEVEL=DEBUG

# Run with verbose output
python agents/API_Agent.py --debug
```

### Performance Monitoring
- Use APM tools (New Relic, DataDog)
- Monitor resource usage
- Track response times
- Set up alerting

## 📈 Maintenance

### Regular Tasks
- Update dependencies monthly
- Monitor API usage and costs
- Review and rotate API keys
- Update documentation
- Performance optimization

### Version Updates
- Test in staging environment
- Gradual rollout strategy
- Rollback procedures
- Change documentation

---

**For additional support, please refer to the main README.md or create an issue in the repository.**
