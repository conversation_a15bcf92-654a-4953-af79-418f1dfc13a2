# 🔧 Troubleshooting Guide

This guide helps you resolve common issues with the Multi-Agent Finance Assistant.

## 🚨 Quick Fixes

### 1. Streamlit Button Error
**Error**: `st.experimental_rerun()` or button-related errors
**Solution**: 
```bash
# Update Streamlit to latest version
pip install --upgrade streamlit

# Or use the fixed version
python start_simple.py
```

### 2. Agent Connection Issues
**Error**: Agents failing to connect or start
**Solution**:
```bash
# Test individual agents first
python test_individual_agent.py

# Check for port conflicts
lsof -i :8000-8006,8501

# Kill conflicting processes
python stop_simple.py
```

### 3. Missing Dependencies
**Error**: `ModuleNotFoundError` or import errors
**Solution**:
```bash
# Install all dependencies
pip install -r requirements.txt

# For Apple M2 Mac, ensure PyTorch with MPS support
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

## 🔍 Diagnostic Steps

### Step 1: Check Environment
```bash
# Verify Python version (3.11+ required)
python --version

# Check if .env file exists
ls -la .env

# Verify API keys are set
cat .env | grep -E "(ALPHA_VANTAGE|FMP_API)"
```

### Step 2: Test Dependencies
```bash
# Run dependency check
python test_individual_agent.py

# Test specific packages
python -c "import torch; print(torch.__version__)"
python -c "import transformers; print(transformers.__version__)"
python -c "import faiss; print('FAISS OK')"
```

### Step 3: Test Individual Agents
```bash
# Test each agent separately
python test_individual_agent.py

# Or test manually:
python agents/API_Agent.py &
curl http://localhost:8001/health
```

## 🐛 Common Issues

### Issue 1: TinyLlama Model Download Fails
**Symptoms**: Language Agent fails to start, model download errors
**Solutions**:
```bash
# Check internet connection
ping huggingface.co

# Clear cache and retry
rm -rf ~/.cache/huggingface/
python agents/Language_Agent.py

# Use smaller model if needed
# Edit .env: LOCAL_LLM_MODEL=distilbert-base-uncased
```

### Issue 2: Port Already in Use
**Symptoms**: "Address already in use" errors
**Solutions**:
```bash
# Find processes using ports
lsof -i :8000-8006,8501

# Kill specific process
kill -9 <PID>

# Or kill all related processes
python stop_simple.py
pkill -f "python.*agents"
```

### Issue 3: API Key Issues
**Symptoms**: "API key not found" or "Unauthorized" errors
**Solutions**:
```bash
# Check .env file
cat .env

# Get free Alpha Vantage key
# Visit: https://www.alphavantage.co/support/#api-key

# For FMP API, use demo key
echo "FMP_API_KEY=demo" >> .env
```

### Issue 4: Memory Issues (Apple M2)
**Symptoms**: System slowdown, out of memory errors
**Solutions**:
```bash
# Use TinyLlama instead of Llama 8B
# Edit .env: LOCAL_LLM_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0

# Monitor memory usage
htop

# Close other applications
# Restart if necessary
```

### Issue 5: Streamlit Not Loading
**Symptoms**: Streamlit page doesn't load or shows errors
**Solutions**:
```bash
# Check if Streamlit is running
curl http://localhost:8501

# Restart Streamlit
pkill -f streamlit
streamlit run streamlit_app/main.py --server.port 8501

# Check for browser cache issues
# Try incognito/private browsing mode
```

## 🔧 Advanced Troubleshooting

### Debug Mode
```bash
# Run agents with debug output
export LOG_LEVEL=DEBUG
python agents/API_Agent.py

# Check logs
tail -f logs/*.log
```

### Network Issues
```bash
# Test API connectivity
curl "https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=AAPL&apikey=demo"

# Test local agent connectivity
curl http://localhost:8001/health
curl http://localhost:8000/health
```

### Clean Restart
```bash
# Complete system reset
python stop_simple.py
pkill -f python
sleep 5
python start_simple.py
```

## 📋 System Requirements Check

### Minimum Requirements
- **OS**: macOS 10.15+ (for M2 Mac)
- **Python**: 3.11+
- **RAM**: 8GB (16GB recommended)
- **Storage**: 5GB free space
- **Network**: Internet connection for initial setup

### Verify System
```bash
# Check macOS version
sw_vers

# Check available RAM
sysctl hw.memsize

# Check free disk space
df -h

# Check Python version
python --version
```

## 🆘 Getting Help

### Log Collection
```bash
# Collect system info
python --version > debug_info.txt
pip list >> debug_info.txt
cat .env >> debug_info.txt

# Collect logs
cp logs/*.log debug_logs/
```

### Common Error Messages

#### "No module named 'torch'"
```bash
pip install torch torchvision torchaudio
```

#### "FAISS not found"
```bash
pip install faiss-cpu
```

#### "Streamlit command not found"
```bash
pip install streamlit
```

#### "Port 8000 already in use"
```bash
lsof -ti:8000 | xargs kill -9
```

#### "Model download timeout"
```bash
# Increase timeout or use smaller model
export HF_HUB_DOWNLOAD_TIMEOUT=300
```

## 🔄 Reset Instructions

### Complete Reset
```bash
# 1. Stop all services
python stop_simple.py

# 2. Clear cache
rm -rf ~/.cache/huggingface/
rm -rf data/faiss_index/*
rm -rf logs/*

# 3. Reset environment
cp .env.template .env
# Edit .env with your API keys

# 4. Reinstall dependencies
pip install -r requirements.txt

# 5. Test system
python test_individual_agent.py

# 6. Start system
python start_simple.py
```

### Partial Reset (Keep Models)
```bash
# Stop services
python stop_simple.py

# Clear only logs and data
rm -rf logs/*
rm -rf data/faiss_index/*

# Restart
python start_simple.py
```

## 📞 Support

If you're still experiencing issues:

1. **Check the logs**: Look in `logs/` directory for error messages
2. **Run diagnostics**: Use `python test_individual_agent.py`
3. **Check system resources**: Ensure sufficient RAM and disk space
4. **Verify network**: Test API connectivity
5. **Try clean restart**: Follow reset instructions above

For additional help, please create an issue with:
- Error messages
- System information
- Steps to reproduce
- Log files (if applicable)
