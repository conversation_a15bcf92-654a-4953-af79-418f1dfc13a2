#!/usr/bin/env python3
"""
Test individual agents to debug connection issues
"""

import subprocess
import time
import requests
import sys
import os

def test_agent_standalone(agent_name, agent_file, port):
    """Test a single agent in standalone mode"""
    print(f"\n🧪 Testing {agent_name} standalone...")
    print("=" * 50)
    
    # Start the agent
    print(f"🚀 Starting {agent_name}...")
    try:
        process = subprocess.Popen([
            sys.executable, agent_file
        ], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True)
        
        # Wait for startup
        print("⏳ Waiting for startup...")
        time.sleep(5)
        
        # Check if process is still running
        if process.poll() is not None:
            stdout, _ = process.communicate()
            print(f"❌ {agent_name} process died")
            print("Output:")
            print(stdout)
            return False
        
        # Test health endpoint
        print(f"🔍 Testing health endpoint...")
        try:
            response = requests.get(f"http://localhost:{port}/health", timeout=5)
            if response.status_code == 200:
                print(f"✅ {agent_name} health check passed")
                print(f"Response: {response.json()}")
                
                # Test a simple endpoint if available
                if agent_name == "API Agent":
                    try:
                        response = requests.get(f"http://localhost:{port}/stock-summary?symbol=AAPL", timeout=10)
                        if response.status_code == 200:
                            print("✅ Stock summary endpoint works")
                        else:
                            print(f"⚠️  Stock summary returned {response.status_code}")
                    except Exception as e:
                        print(f"⚠️  Stock summary test failed: {e}")
                
                # Stop the process
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                
                return True
                
            else:
                print(f"❌ Health check failed: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Could not connect to {agent_name}: {e}")
            
        # Stop the process
        process.terminate()
        try:
            process.wait(timeout=5)
        except subprocess.TimeoutExpired:
            process.kill()
            
        return False
        
    except Exception as e:
        print(f"❌ Error testing {agent_name}: {e}")
        return False

def check_dependencies():
    """Check if all dependencies are available"""
    print("🔍 Checking dependencies...")
    
    required_packages = {
        'fastapi': 'FastAPI web framework',
        'uvicorn': 'ASGI server',
        'requests': 'HTTP client',
        'pandas': 'Data analysis',
        'yfinance': 'Yahoo Finance data',
        'torch': 'PyTorch for ML',
        'transformers': 'Hugging Face transformers',
        'sentence_transformers': 'Sentence embeddings',
        'faiss': 'Vector search (faiss-cpu)',
        'streamlit': 'Web UI framework'
    }
    
    missing = []
    for package, description in required_packages.items():
        try:
            if package == 'faiss':
                import faiss
            else:
                __import__(package)
            print(f"✅ {package}: {description}")
        except ImportError:
            print(f"❌ {package}: {description} - MISSING")
            missing.append(package)
    
    if missing:
        print(f"\n❌ Missing packages: {', '.join(missing)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies found")
    return True

def check_environment():
    """Check environment configuration"""
    print("\n🔍 Checking environment...")
    
    # Check .env file
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return False
    
    print("✅ .env file found")
    
    # Load and check key variables
    from dotenv import load_dotenv
    load_dotenv()
    
    alpha_vantage = os.getenv('ALPHA_VANTAGE_API')
    fmp_key = os.getenv('FMP_API_KEY')
    llm_model = os.getenv('LOCAL_LLM_MODEL')
    
    if alpha_vantage:
        print(f"✅ Alpha Vantage API key configured")
    else:
        print("⚠️  Alpha Vantage API key not set")
    
    if fmp_key:
        print(f"✅ FMP API key: {fmp_key}")
    else:
        print("⚠️  FMP API key not set")
    
    if llm_model:
        print(f"✅ LLM Model: {llm_model}")
    else:
        print("⚠️  LLM Model not set")
    
    return True

def main():
    """Main test function"""
    print("🧪 Individual Agent Testing")
    print("=" * 50)
    
    # Check dependencies first
    if not check_dependencies():
        print("\n❌ Dependency check failed. Please install missing packages.")
        sys.exit(1)
    
    # Check environment
    if not check_environment():
        print("\n❌ Environment check failed. Please check .env file.")
        sys.exit(1)
    
    # Test each agent individually
    agents = [
        ("API Agent", "agents/API_Agent.py", 8001),
        ("Financial Data Agent", "agents/Scraping_Agent.py", 8002),
        ("Retriever Agent", "agents/Retriever_Agent.py", 8003),
        ("Analysis Agent", "agents/Analysis_Agent.py", 8004),
        ("Language Agent", "agents/Language_Agent.py", 8005),
        ("Text Processing Agent", "agents/Voice_Agent.py", 8006),
    ]
    
    results = []
    for agent_name, agent_file, port in agents:
        if os.path.exists(agent_file):
            success = test_agent_standalone(agent_name, agent_file, port)
            results.append((agent_name, success))
        else:
            print(f"❌ {agent_file} not found")
            results.append((agent_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for agent_name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {status}: {agent_name}")
        if success:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)}")
    
    if passed == len(results):
        print("\n🎉 All agents working! You can now run the full system.")
        print("Run: python start_simple.py")
    else:
        print("\n⚠️  Some agents failed. Check the error messages above.")
        print("Common issues:")
        print("   • Missing API keys in .env file")
        print("   • Missing dependencies")
        print("   • Port conflicts")
        print("   • Network connectivity issues")

if __name__ == "__main__":
    main()
