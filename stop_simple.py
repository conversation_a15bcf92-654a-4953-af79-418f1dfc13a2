#!/usr/bin/env python3
"""
Simple stop script for Multi-Agent Finance Assistant
Stops all running agents and services
"""

import subprocess
import sys
import time

def kill_processes_by_port():
    """Kill processes running on our ports"""
    ports = [8000, 8001, 8002, 8003, 8004, 8005, 8006, 8501]
    
    for port in ports:
        try:
            # Find process using the port
            result = subprocess.run([
                'lsof', '-ti', f':{port}'
            ], capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                for pid in pids:
                    try:
                        subprocess.run(['kill', pid], check=True)
                        print(f"🔴 Killed process {pid} on port {port}")
                    except subprocess.CalledProcessError:
                        try:
                            subprocess.run(['kill', '-9', pid], check=True)
                            print(f"🔴 Force killed process {pid} on port {port}")
                        except subprocess.CalledProcessError:
                            print(f"⚠️  Could not kill process {pid}")
            
        except FileNotFoundError:
            # lsof not available, try alternative method
            pass
        except Exception as e:
            print(f"⚠️  Error checking port {port}: {e}")

def kill_python_agents():
    """Kill Python processes that look like our agents"""
    agent_files = [
        'API_Agent.py',
        'Scraping_Agent.py', 
        'Retriever_Agent.py',
        'Analysis_Agent.py',
        'Language_Agent.py',
        'Voice_Agent.py',
        'orchestrator/main.py',
        'streamlit_app/main.py'
    ]
    
    try:
        # Get all Python processes
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True)
        
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                for agent_file in agent_files:
                    if agent_file in line and 'python' in line:
                        # Extract PID (second column)
                        parts = line.split()
                        if len(parts) > 1:
                            pid = parts[1]
                            try:
                                subprocess.run(['kill', pid], check=True)
                                print(f"🔴 Killed {agent_file} process {pid}")
                            except subprocess.CalledProcessError:
                                try:
                                    subprocess.run(['kill', '-9', pid], check=True)
                                    print(f"🔴 Force killed {agent_file} process {pid}")
                                except subprocess.CalledProcessError:
                                    print(f"⚠️  Could not kill {agent_file} process {pid}")
                        break
    
    except Exception as e:
        print(f"⚠️  Error killing Python agents: {e}")

def main():
    """Main stop function"""
    print("🛑 Stopping Multi-Agent Finance Assistant")
    print("=" * 50)
    
    # Method 1: Kill by port
    print("🔍 Killing processes by port...")
    kill_processes_by_port()
    
    # Wait a moment
    time.sleep(2)
    
    # Method 2: Kill Python agent processes
    print("🔍 Killing Python agent processes...")
    kill_python_agents()
    
    # Method 3: Kill any remaining streamlit processes
    try:
        subprocess.run(['pkill', '-f', 'streamlit'], check=False)
        print("🔴 Killed any remaining Streamlit processes")
    except Exception:
        pass
    
    print("\n✅ Stop script completed")
    print("📝 If any processes are still running, you may need to kill them manually")
    print("   Use: ps aux | grep python")
    print("   Then: kill <PID>")

if __name__ == "__main__":
    main()
