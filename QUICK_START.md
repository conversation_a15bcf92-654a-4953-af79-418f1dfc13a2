# 🚀 Quick Start Guide

Get the Multi-Agent Finance Assistant running in 5 minutes!

## ⚡ Super Quick Start

```bash
# 1. Setup environment
cp .env.template .env
# Edit .env with your Alpha Vantage API key (free at alphavantage.co)

# 2. Install dependencies
pip install -r requirements.txt

# 3. Test the system
python test_individual_agent.py

# 4. Start everything
python start_simple.py

# 5. Open browser
# Streamlit UI: http://localhost:8501
# API Docs: http://localhost:8000/docs
```

## 📋 Prerequisites

- **Python 3.11+** (check with `python --version`)
- **8GB+ RAM** (for TinyLlama model)
- **Internet connection** (for initial model download)

## 🔑 API Keys (Required)

### Alpha Vantage (Free)
1. Go to: https://www.alphavantage.co/support/#api-key
2. Sign up for free account
3. Copy your API key
4. Add to `.env`: `ALPHA_VANTAGE_API=your_key_here`

### Financial Modeling Prep (Optional)
- Use `FMP_API_KEY=demo` for limited access
- Or get free key at: https://financialmodelingprep.com/developer/docs

## 🛠️ Installation

### Option 1: Automatic (Recommended)
```bash
# Clone and setup
git clone <repository-url>
cd RagaAI-Assignment

# Run setup script
python test_individual_agent.py  # This checks everything
python start_simple.py           # This starts everything
```

### Option 2: Manual
```bash
# 1. Create virtual environment
python -m venv RAGA
source RAGA/bin/activate  # On Windows: RAGA\Scripts\activate

# 2. Install packages
pip install -r requirements.txt

# 3. Setup environment
cp .env.template .env
# Edit .env with your API keys

# 4. Create directories
mkdir -p data/faiss_index logs

# 5. Start agents
python start_simple.py
```

## 🧪 Testing

### Quick Test
```bash
# Test all agents
python test_individual_agent.py

# Test API manually
curl http://localhost:8001/health
curl http://localhost:8000/health
```

### Expected Output
```
🧪 Individual Agent Testing
==================================================
✅ Python 3.11 detected
✅ All required packages found
✅ .env file found
✅ Alpha Vantage API key configured

🧪 Testing API Agent standalone...
✅ API Agent health check passed
✅ Stock summary endpoint works

🧪 Testing Financial Data Agent standalone...
✅ Financial Data Agent health check passed

... (all agents should pass)

🎉 All agents working! You can now run the full system.
```

## 🌐 Access Points

Once started, access these URLs:

- **Main UI**: http://localhost:8501 (Streamlit dashboard)
- **API Docs**: http://localhost:8000/docs (Interactive API documentation)
- **Health Check**: http://localhost:8000/health (System status)

## 🎯 First Use

### 1. Market Brief
1. Go to http://localhost:8501
2. Click "Market Brief" tab
3. Enter query: "What's the outlook for tech stocks?"
4. Select symbols: AAPL, GOOGL, TSLA
5. Click "Generate Market Brief"

### 2. Portfolio Analysis
1. Click "Portfolio Analysis" tab
2. Enter your positions (symbol, shares, cost)
3. Click "Analyze Portfolio"
4. View risk metrics and recommendations

### 3. Text Processing
1. Click "Text Processing" tab
2. Enter financial text
3. Choose operation (analyze/summarize/format)
4. Click "Process Text"

## 🔧 Troubleshooting

### Common Issues

#### "Module not found" errors
```bash
pip install -r requirements.txt
```

#### "Port already in use"
```bash
python stop_simple.py
```

#### Streamlit button errors
```bash
pip install --upgrade streamlit
```

#### Model download fails
```bash
# Check internet connection
ping huggingface.co

# Clear cache and retry
rm -rf ~/.cache/huggingface/
python start_simple.py
```

### Get Help
- **Full troubleshooting**: See `TROUBLESHOOTING.md`
- **Test individual agents**: `python test_individual_agent.py`
- **Check logs**: Look in `logs/` directory

## 🛑 Stopping the System

```bash
# Graceful stop
python stop_simple.py

# Or press Ctrl+C in the terminal running start_simple.py
```

## 📊 Performance Tips

### For Apple M2 Mac
- **TinyLlama**: Fast, 3-4GB RAM usage
- **Llama 8B**: Better quality, 12-16GB RAM usage
- **MPS acceleration**: Automatically enabled

### Memory Optimization
```bash
# Use smaller model (edit .env)
LOCAL_LLM_MODEL=TinyLlama/TinyLlama-1.1B-Chat-v1.0

# Monitor usage
htop  # or Activity Monitor
```

## 🎓 Next Steps

### Explore Features
- Try different stock symbols
- Test portfolio analysis with your real positions
- Experiment with text processing
- Check out the API documentation

### Customize
- Edit `.env` for different models
- Modify agent configurations
- Add new data sources
- Integrate with your own systems

### Advanced Usage
- See full `README.md` for architecture details
- Check `docs/` for detailed guides
- Explore API endpoints at `/docs`

## 📈 Example Queries

### Market Brief
- "What's the risk exposure in tech stocks today?"
- "Analyze Apple's recent performance"
- "Compare TSLA vs traditional automakers"

### Portfolio Analysis
- Add positions: AAPL (100 shares @ $150), GOOGL (50 shares @ $2500)
- Get risk metrics, sector allocation, recommendations

### Text Processing
- Analyze: "Tesla reported record quarterly deliveries"
- Summarize: Long financial articles
- Format: Poorly formatted financial text

---

**🎉 You're ready to go! Start with `python start_simple.py`**
