import pandas as pd
import numpy as np
from typing import List, Dict, Any, Optional
import logging
from datetime import datetime, timedelta
import yfinance as yf
import requests
from bs4 import BeautifulSoup
import json
import asyncio
import aiohttp
from pathlib import Path
import pickle

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DataProcessor:
    """Main data processing and ingestion class"""
    
    def __init__(self, data_dir: str = "./data"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (self.data_dir / "raw").mkdir(exist_ok=True)
        (self.data_dir / "processed").mkdir(exist_ok=True)
        (self.data_dir / "embeddings").mkdir(exist_ok=True)
    
    def fetch_stock_data(self, symbols: List[str], period: str = "1y") -> Dict[str, pd.DataFrame]:
        """Fetch stock data for multiple symbols"""
        stock_data = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                data = ticker.history(period=period)
                
                if not data.empty:
                    stock_data[symbol] = data
                    logger.info(f"Fetched data for {symbol}: {len(data)} records")
                else:
                    logger.warning(f"No data found for {symbol}")
                    
            except Exception as e:
                logger.error(f"Error fetching data for {symbol}: {e}")
                continue
        
        return stock_data
    
    def fetch_company_info(self, symbols: List[str]) -> Dict[str, Dict]:
        """Fetch company information for symbols"""
        company_info = {}
        
        for symbol in symbols:
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info
                
                # Extract key information
                company_info[symbol] = {
                    'name': info.get('longName', symbol),
                    'sector': info.get('sector', 'Unknown'),
                    'industry': info.get('industry', 'Unknown'),
                    'market_cap': info.get('marketCap'),
                    'employees': info.get('fullTimeEmployees'),
                    'country': info.get('country', 'Unknown'),
                    'website': info.get('website'),
                    'business_summary': info.get('longBusinessSummary', '')
                }
                
                logger.info(f"Fetched company info for {symbol}")
                
            except Exception as e:
                logger.error(f"Error fetching company info for {symbol}: {e}")
                continue
        
        return company_info
    
    def process_news_data(self, news_articles: List[Dict]) -> List[Dict]:
        """Process and clean news data"""
        processed_articles = []
        
        for article in news_articles:
            try:
                # Clean and validate article data
                processed_article = {
                    'title': article.get('title', '').strip(),
                    'url': article.get('url', ''),
                    'source': article.get('source', 'Unknown'),
                    'published_date': article.get('published_date'),
                    'summary': article.get('summary', '').strip(),
                    'sentiment': article.get('sentiment', 'neutral'),
                    'processed_at': datetime.now().isoformat()
                }
                
                # Skip articles with missing essential data
                if not processed_article['title'] or not processed_article['url']:
                    continue
                
                processed_articles.append(processed_article)
                
            except Exception as e:
                logger.error(f"Error processing article: {e}")
                continue
        
        return processed_articles
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate technical indicators for stock data"""
        try:
            # Simple Moving Averages
            df['SMA_20'] = df['Close'].rolling(window=20).mean()
            df['SMA_50'] = df['Close'].rolling(window=50).mean()
            df['SMA_200'] = df['Close'].rolling(window=200).mean()
            
            # Exponential Moving Averages
            df['EMA_12'] = df['Close'].ewm(span=12).mean()
            df['EMA_26'] = df['Close'].ewm(span=26).mean()
            
            # MACD
            df['MACD'] = df['EMA_12'] - df['EMA_26']
            df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
            df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']
            
            # RSI
            delta = df['Close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
            rs = gain / loss
            df['RSI'] = 100 - (100 / (1 + rs))
            
            # Bollinger Bands
            df['BB_Middle'] = df['Close'].rolling(window=20).mean()
            bb_std = df['Close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
            df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
            
            # Volume indicators
            df['Volume_SMA'] = df['Volume'].rolling(window=20).mean()
            df['Volume_Ratio'] = df['Volume'] / df['Volume_SMA']
            
            # Price change indicators
            df['Daily_Return'] = df['Close'].pct_change()
            df['Volatility_20'] = df['Daily_Return'].rolling(window=20).std()
            
            logger.info("Technical indicators calculated successfully")
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
        
        return df
    
    def create_market_summary(self, stock_data: Dict[str, pd.DataFrame]) -> Dict:
        """Create market summary from stock data"""
        try:
            summary = {
                'timestamp': datetime.now().isoformat(),
                'symbols_analyzed': list(stock_data.keys()),
                'market_metrics': {}
            }
            
            for symbol, df in stock_data.items():
                if df.empty:
                    continue
                
                latest = df.iloc[-1]
                previous = df.iloc[-2] if len(df) > 1 else latest
                
                # Calculate metrics
                daily_change = latest['Close'] - previous['Close']
                daily_change_pct = (daily_change / previous['Close']) * 100
                
                # Volatility (20-day)
                returns = df['Close'].pct_change().dropna()
                volatility = returns.tail(20).std() * np.sqrt(252)  # Annualized
                
                summary['market_metrics'][symbol] = {
                    'current_price': round(latest['Close'], 2),
                    'daily_change': round(daily_change, 2),
                    'daily_change_pct': round(daily_change_pct, 2),
                    'volume': int(latest['Volume']),
                    'volatility_20d': round(volatility, 4),
                    'high_52w': round(df['High'].tail(252).max(), 2),
                    'low_52w': round(df['Low'].tail(252).min(), 2)
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error creating market summary: {e}")
            return {}
    
    def save_processed_data(self, data: Any, filename: str, format: str = "pickle"):
        """Save processed data to disk"""
        try:
            filepath = self.data_dir / "processed" / filename
            
            if format == "pickle":
                with open(filepath, 'wb') as f:
                    pickle.dump(data, f)
            elif format == "json":
                with open(filepath, 'w') as f:
                    json.dump(data, f, indent=2, default=str)
            elif format == "csv" and isinstance(data, pd.DataFrame):
                data.to_csv(filepath, index=True)
            else:
                raise ValueError(f"Unsupported format: {format}")
            
            logger.info(f"Data saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving data: {e}")
    
    def load_processed_data(self, filename: str, format: str = "pickle"):
        """Load processed data from disk"""
        try:
            filepath = self.data_dir / "processed" / filename
            
            if not filepath.exists():
                logger.warning(f"File not found: {filepath}")
                return None
            
            if format == "pickle":
                with open(filepath, 'rb') as f:
                    return pickle.load(f)
            elif format == "json":
                with open(filepath, 'r') as f:
                    return json.load(f)
            elif format == "csv":
                return pd.read_csv(filepath, index_col=0)
            else:
                raise ValueError(f"Unsupported format: {format}")
                
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            return None
    
    def prepare_documents_for_indexing(self, 
                                     news_articles: List[Dict], 
                                     company_info: Dict[str, Dict]) -> List[Dict]:
        """Prepare documents for vector indexing"""
        documents = []
        
        # Process news articles
        for i, article in enumerate(news_articles):
            doc_id = f"news_{i}_{hash(article.get('url', ''))}"
            content = f"{article.get('title', '')} {article.get('summary', '')}"
            
            documents.append({
                'id': doc_id,
                'content': content.strip(),
                'metadata': {
                    'type': 'news',
                    'source': article.get('source', 'Unknown'),
                    'url': article.get('url', ''),
                    'published_date': article.get('published_date'),
                    'sentiment': article.get('sentiment', 'neutral')
                }
            })
        
        # Process company information
        for symbol, info in company_info.items():
            doc_id = f"company_{symbol}"
            content = f"{info.get('name', '')} {info.get('business_summary', '')}"
            
            documents.append({
                'id': doc_id,
                'content': content.strip(),
                'metadata': {
                    'type': 'company_info',
                    'symbol': symbol,
                    'sector': info.get('sector', 'Unknown'),
                    'industry': info.get('industry', 'Unknown'),
                    'country': info.get('country', 'Unknown')
                }
            })
        
        return documents
    
    async def run_full_ingestion_pipeline(self, symbols: List[str]) -> Dict:
        """Run complete data ingestion pipeline"""
        try:
            logger.info(f"Starting data ingestion for symbols: {symbols}")
            
            # Step 1: Fetch stock data
            stock_data = self.fetch_stock_data(symbols)
            
            # Step 2: Calculate technical indicators
            for symbol in stock_data:
                stock_data[symbol] = self.calculate_technical_indicators(stock_data[symbol])
            
            # Step 3: Fetch company information
            company_info = self.fetch_company_info(symbols)
            
            # Step 4: Create market summary
            market_summary = self.create_market_summary(stock_data)
            
            # Step 5: Save processed data
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.save_processed_data(stock_data, f"stock_data_{timestamp}.pickle")
            self.save_processed_data(company_info, f"company_info_{timestamp}.json", "json")
            self.save_processed_data(market_summary, f"market_summary_{timestamp}.json", "json")
            
            logger.info("Data ingestion pipeline completed successfully")
            
            return {
                'status': 'success',
                'symbols_processed': symbols,
                'stock_data_records': {symbol: len(df) for symbol, df in stock_data.items()},
                'company_info_count': len(company_info),
                'timestamp': timestamp
            }
            
        except Exception as e:
            logger.error(f"Error in data ingestion pipeline: {e}")
            return {
                'status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

# Example usage
if __name__ == "__main__":
    processor = DataProcessor()
    
    # Example symbols
    symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "NVDA"]
    
    # Run ingestion pipeline
    import asyncio
    result = asyncio.run(processor.run_full_ingestion_pipeline(symbols))
    print(json.dumps(result, indent=2))
